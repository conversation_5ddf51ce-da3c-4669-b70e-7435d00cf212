package com.chua.attach.hotspot.core.support.plugin;

import com.chua.attach.hotspot.core.support.inst.InstrumentationFactory;
import com.chua.attach.hotspot.core.support.span.NewTrackManager;
import com.chua.attach.hotspot.core.support.span.Span;
import com.chua.attach.hotspot.core.support.utils.StringUtils;
import javassist.ClassPool;
import javassist.CtClass;
import javassist.CtConstructor;
import javassist.LoaderClassPath;
import net.bytebuddy.description.type.TypeDescription;
import net.bytebuddy.dynamic.DynamicType;
import net.bytebuddy.matcher.ElementMatcher;

import java.lang.reflect.Method;
import java.text.MessageFormat;

/**
 * <AUTHOR>
 */
public class ExceptPlugin implements Plugin {
    @Override
    public String name() {
        return "Exception";
    }

    @Override
    public void init() {
    }

    @Override
    public DynamicType.Builder.MethodDefinition.ReceiverTypeDefinition<?> transform(DynamicType.Builder<?> builder) {
//        return builder.method(ElementMatchers.any()).intercept(MethodDelegation.to(ExceptPlugin.class));
        return null;
    }


    @Override
    public ElementMatcher<? super TypeDescription> type() {
//        return ElementMatchers.named("java.lang.Throwable")
//                .or(ElementMatchers.named(Exception.class.getTypeName()))
//                ;
        return null;
    }

    @Override
    public void initComplete() {
        Class<?> type = InstrumentationFactory.getInstance().getType(RuntimeException.class.getTypeName());
        try {
            InstrumentationFactory.getInstance().rebase(type, createThrowable(type.getName(), type, null));
        } catch (Exception ignored) {
        }
    }
    public static void register(Object bean, Object[] args) throws Exception {
        if(args[0] instanceof ArrayIndexOutOfBoundsException) {
            return;
        }

        if(bean instanceof ClassCastException) {
            return;
        }
        Span lastSpan = NewTrackManager.getLastSpan();
        if(null == lastSpan) {
            return;
        }
        RuntimeException runtimeException = (RuntimeException) bean;
        StackTraceElement[] stackTrace = runtimeException.getStackTrace();

        StackTraceElement first = stackTrace[0];
        for (StackTraceElement stackTraceElement : stackTrace) {
            if(Span.pass(stackTraceElement)) {
                continue;
            }
            first = stackTraceElement;
            break;

        }
        String type = first.getClassName();

        if(type.startsWith("org.apache")) {
            return;
        }
        if(type.startsWith("org.spring")) {
            return;
        }
        Span entrySpan = NewTrackManager.createEntrySpan(new Object[0]);
        entrySpan.setPid(lastSpan.getPid());
        entrySpan.setType(first.getClassName());
        entrySpan.setStackTrace(stackTrace);
        entrySpan.setMethod(first.getMethodName());
        entrySpan.setMessage(MessageFormat.format("<span style=''color:red''>链路追踪(Exception): {0}.{1}{2} => {3}</span>",
                first.getClassName(),
                first.getMethodName(),
                "",
                runtimeException.getMessage()));
        entrySpan.setError(first.getClassName() +":"+ first.getLineNumber());
        entrySpan.addTip(runtimeException.getMessage());
        entrySpan.addTip(entrySpan.getError());

        Span fisrtSpan = NewTrackManager.getFisrtSpan();
        if(null != fisrtSpan && !StringUtils.isEmpty(fisrtSpan.getMessage())) {
            fisrtSpan.setHasException(true);
        }

    }
    private byte[] createThrowable(String className, Class<?> classBeingRedefined, byte[] classfileBuffer) {
        ClassPool classPool = ClassPool.getDefault();
        ClassLoader contextClassLoader = Thread.currentThread().getContextClassLoader();
        classPool.importPackage(Method.class.getTypeName());
        classPool.importPackage(Throwable.class.getTypeName());
        classPool.importPackage(ExceptPlugin.class.getTypeName());
        classPool.insertClassPath(new LoaderClassPath(contextClassLoader));
        try {
            CtClass ctClass = classPool.get(className);
            CtConstructor[] constructors = ctClass.getConstructors();
            for (CtConstructor constructor : constructors) {
                constructor.insertAfter("{try{" +
                        "Class type = ClassLoader.getSystemClassLoader().loadClass(\""+ this.getClass().getTypeName()+"\");" +
                        "Method method = type.getDeclaredMethod(\"register\", new Class[]{Object.class, Object[].class});" +
                        "method.setAccessible(true);" +
                        "method.invoke(null, new Object[]{this, $args});}catch(Throwable ignore){}}");

//                constructor.insertAfter(this.getClass().getTypeName()+".register(this, $args);");
            }
            byte[] bytes = ctClass.toBytecode();
            ctClass.detach();
            return bytes;
        } catch (Exception e) {
            return classfileBuffer;
        }

    }

    @Override
    public void finish() {
        try {
            InstrumentationFactory.getInstance().get().retransformClasses(Exception.class);
        } catch (Exception e) {
           e.printStackTrace();
        }
    }
}
