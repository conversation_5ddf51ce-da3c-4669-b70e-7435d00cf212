package com.chua.docker.support.client;

import com.chua.common.support.annotations.Spi;
import com.chua.common.support.lang.exception.RemoteExecutionException;
import com.chua.common.support.protocol.ClientSetting;
import com.chua.common.support.protocol.client.AbstractClient;
import com.chua.common.support.utils.StringUtils;
import com.chua.docker.support.session.DockerChannelSession;
import com.github.dockerjava.api.DockerClientBuilder;
import com.github.dockerjava.api.command.*;
import com.github.dockerjava.api.model.*;
import com.github.dockerjava.core.DefaultDockerClientConfig;
import com.github.dockerjava.core.DockerClientConfig;
import com.github.dockerjava.httpclient5.ApacheDockerHttpClient;
import com.github.dockerjava.transport.DockerHttpClient;
import lombok.extern.slf4j.Slf4j;

import java.io.Closeable;
import java.io.IOException;
import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Docker 客户端
 * 
 * 提供 Docker 容器和镜像管理功能，基于 Docker Java API 实现。
 * 支持容器的创建、启动、停止、删除，镜像的拉取、构建、推送等操作。
 * 
 * <AUTHOR>
 * @since 2024/12/25
 */
@Slf4j
@Spi("docker")
public class DockerClient extends AbstractClient implements Closeable {

    private com.github.dockerjava.api.DockerClient dockerClient;
    private DockerHttpClient httpClient;

    public DockerClient(ClientSetting clientSetting) {
        super(clientSetting);
        initializeDockerClient();
    }

    /**
     * 初始化 Docker 客户端
     */
    private void initializeDockerClient() {
        try {
            // 构建 Docker 客户端配置
            DockerClientConfig config = buildDockerConfig();
            
            // 创建 HTTP 客户端
            httpClient = new ApacheDockerHttpClient.Builder()
                    .dockerHost(config.getDockerHost())
                    .sslConfig(config.getSSLConfig())
                    .maxConnections(100)
                    .connectionTimeout(Duration.ofSeconds(30))
                    .responseTimeout(Duration.ofSeconds(45))
                    .build();
            
            // 创建 Docker 客户端
            dockerClient = DockerClientBuilder.getInstance(config)
                    .withDockerHttpClient(httpClient)
                    .build();
                    
            log.info("Docker 客户端初始化成功，连接到: {}", config.getDockerHost());
        } catch (Exception e) {
            log.error("Docker 客户端初始化失败", e);
            throw new RemoteExecutionException("docker", "Docker 客户端初始化失败: " + e.getMessage());
        }
    }

    /**
     * 构建 Docker 客户端配置
     */
    private DockerClientConfig buildDockerConfig() {
        DefaultDockerClientConfig.Builder configBuilder = DefaultDockerClientConfig.createDefaultConfigBuilder();
        
        // 设置 Docker 主机
        String dockerHost = getDockerHost();
        if (StringUtils.isNotBlank(dockerHost)) {
            configBuilder.withDockerHost(dockerHost);
        }
        
        // 设置 TLS 验证
        if (clientSetting.isSsl()) {
            configBuilder.withDockerTlsVerify(true);
            if (StringUtils.isNotBlank(clientSetting.certPath())) {
                configBuilder.withDockerCertPath(clientSetting.certPath());
            }
        }
        
        // 设置 API 版本
        if (StringUtils.isNotBlank(clientSetting.version())) {
            configBuilder.withApiVersion(clientSetting.version());
        }
        
        // 设置注册表配置
        if (StringUtils.isNotBlank(clientSetting.username()) && StringUtils.isNotBlank(clientSetting.password())) {
            configBuilder.withRegistryUsername(clientSetting.username())
                          .withRegistryPassword(clientSetting.password());
            
            if (StringUtils.isNotBlank(clientSetting.email())) {
                configBuilder.withRegistryEmail(clientSetting.email());
            }
            
            if (StringUtils.isNotBlank(clientSetting.url())) {
                configBuilder.withRegistryUrl(clientSetting.url());
            }
        }
        
        return configBuilder.build();
    }

    /**
     * 获取 Docker 主机地址
     */
    private String getDockerHost() {
        if (StringUtils.isNotBlank(clientSetting.url())) {
            return clientSetting.url();
        }
        
        if (StringUtils.isNotBlank(clientSetting.host())) {
            String protocol = clientSetting.isSsl() ? "https" : "tcp";
            int port = clientSetting.port() > 0 ? clientSetting.port() : 2376;
            return String.format("%s://%s:%d", protocol, clientSetting.host(), port);
        }
        
        // 使用默认的 Docker 主机
        return null;
    }

    @Override
    public DockerChannelSession createSession() {
        return new DockerChannelSession(dockerClient, clientSetting);
    }

    /**
     * 获取原生 Docker 客户端
     */
    public com.github.dockerjava.api.DockerClient getNativeClient() {
        return dockerClient;
    }

    /**
     * 测试 Docker 连接
     */
    public boolean testConnection() {
        try {
            Info info = dockerClient.infoCmd().exec();
            log.info("Docker 连接测试成功，版本: {}", info.getServerVersion());
            return true;
        } catch (Exception e) {
            log.error("Docker 连接测试失败", e);
            return false;
        }
    }

    /**
     * 获取 Docker 系统信息
     */
    public Info getDockerInfo() {
        try {
            return dockerClient.infoCmd().exec();
        } catch (Exception e) {
            log.error("获取 Docker 系统信息失败", e);
            throw new RemoteExecutionException("docker", "获取 Docker 系统信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取 Docker 版本信息
     */
    public Version getDockerVersion() {
        try {
            return dockerClient.versionCmd().exec();
        } catch (Exception e) {
            log.error("获取 Docker 版本信息失败", e);
            throw new RemoteExecutionException("docker", "获取 Docker 版本信息失败: " + e.getMessage());
        }
    }

    /**
     * Ping Docker 守护进程
     */
    public void ping() {
        try {
            dockerClient.pingCmd().exec();
            log.debug("Docker ping 成功");
        } catch (Exception e) {
            log.error("Docker ping 失败", e);
            throw new RemoteExecutionException("docker", "Docker ping 失败: " + e.getMessage());
        }
    }

    // ==================== 容器管理方法 ====================

    /**
     * 列出所有容器
     * @param showAll 是否显示所有容器（包括停止的）
     * @return 容器列表
     */
    public List<Container> listContainers(boolean showAll) {
        try {
            return dockerClient.listContainersCmd()
                    .withShowAll(showAll)
                    .exec();
        } catch (Exception e) {
            log.error("列出容器失败", e);
            throw new RemoteExecutionException("docker", "列出容器失败: " + e.getMessage());
        }
    }

    /**
     * 创建容器
     * @param imageName 镜像名称
     * @param containerName 容器名称
     * @return 容器创建响应
     */
    public CreateContainerResponse createContainer(String imageName, String containerName) {
        try {
            CreateContainerCmd cmd = dockerClient.createContainerCmd(imageName);
            if (StringUtils.isNotBlank(containerName)) {
                cmd.withName(containerName);
            }
            return cmd.exec();
        } catch (Exception e) {
            log.error("创建容器失败: {}", imageName, e);
            throw new RemoteExecutionException("docker", "创建容器失败: " + e.getMessage());
        }
    }

    /**
     * 创建容器（高级配置）
     * @param imageName 镜像名称
     * @param containerName 容器名称
     * @param portBindings 端口绑定
     * @param env 环境变量
     * @param volumes 卷挂载
     * @return 容器创建响应
     */
    public CreateContainerResponse createContainer(String imageName, String containerName,
                                                 Map<String, String> portBindings,
                                                 List<String> env,
                                                 Map<String, String> volumes) {
        try {
            CreateContainerCmd cmd = dockerClient.createContainerCmd(imageName);

            if (StringUtils.isNotBlank(containerName)) {
                cmd.withName(containerName);
            }

            // 设置端口绑定
            if (portBindings != null && !portBindings.isEmpty()) {
                PortBinding[] bindings = portBindings.entrySet().stream()
                        .map(entry -> PortBinding.parse(entry.getKey() + ":" + entry.getValue()))
                        .toArray(PortBinding[]::new);
                cmd.withPortBindings(bindings);
            }

            // 设置环境变量
            if (env != null && !env.isEmpty()) {
                cmd.withEnv(env);
            }

            // 设置卷挂载
            if (volumes != null && !volumes.isEmpty()) {
                Volume[] volumeArray = volumes.keySet().stream()
                        .map(Volume::new)
                        .toArray(Volume[]::new);
                cmd.withVolumes(volumeArray);

                Bind[] binds = volumes.entrySet().stream()
                        .map(entry -> new Bind(entry.getValue(), new Volume(entry.getKey())))
                        .toArray(Bind[]::new);
                cmd.withBinds(binds);
            }

            return cmd.exec();
        } catch (Exception e) {
            log.error("创建容器失败: {}", imageName, e);
            throw new RemoteExecutionException("docker", "创建容器失败: " + e.getMessage());
        }
    }

    /**
     * 启动容器
     * @param containerId 容器ID
     */
    public void startContainer(String containerId) {
        try {
            dockerClient.startContainerCmd(containerId).exec();
            log.info("容器启动成功: {}", containerId);
        } catch (Exception e) {
            log.error("启动容器失败: {}", containerId, e);
            throw new RemoteExecutionException("docker", "启动容器失败: " + e.getMessage());
        }
    }

    /**
     * 停止容器
     * @param containerId 容器ID
     * @param timeout 超时时间（秒）
     */
    public void stopContainer(String containerId, int timeout) {
        try {
            dockerClient.stopContainerCmd(containerId)
                    .withTimeout(timeout)
                    .exec();
            log.info("容器停止成功: {}", containerId);
        } catch (Exception e) {
            log.error("停止容器失败: {}", containerId, e);
            throw new RemoteExecutionException("docker", "停止容器失败: " + e.getMessage());
        }
    }

    /**
     * 重启容器
     * @param containerId 容器ID
     * @param timeout 超时时间（秒）
     */
    public void restartContainer(String containerId, int timeout) {
        try {
            dockerClient.restartContainerCmd(containerId)
                    .withTimeout(timeout)
                    .exec();
            log.info("容器重启成功: {}", containerId);
        } catch (Exception e) {
            log.error("重启容器失败: {}", containerId, e);
            throw new RemoteExecutionException("docker", "重启容器失败: " + e.getMessage());
        }
    }

    /**
     * 删除容器
     * @param containerId 容器ID
     * @param force 是否强制删除
     * @param removeVolumes 是否删除关联的卷
     */
    public void removeContainer(String containerId, boolean force, boolean removeVolumes) {
        try {
            dockerClient.removeContainerCmd(containerId)
                    .withForce(force)
                    .withRemoveVolumes(removeVolumes)
                    .exec();
            log.info("容器删除成功: {}", containerId);
        } catch (Exception e) {
            log.error("删除容器失败: {}", containerId, e);
            throw new RemoteExecutionException("docker", "删除容器失败: " + e.getMessage());
        }
    }

    /**
     * 获取容器详细信息
     * @param containerId 容器ID
     * @return 容器信息
     */
    public InspectContainerResponse inspectContainer(String containerId) {
        try {
            return dockerClient.inspectContainerCmd(containerId).exec();
        } catch (Exception e) {
            log.error("获取容器信息失败: {}", containerId, e);
            throw new RemoteExecutionException("docker", "获取容器信息失败: " + e.getMessage());
        }
    }

    // ==================== 镜像管理方法 ====================

    /**
     * 列出所有镜像
     * @return 镜像列表
     */
    public List<Image> listImages() {
        try {
            return dockerClient.listImagesCmd().exec();
        } catch (Exception e) {
            log.error("列出镜像失败", e);
            throw new RemoteExecutionException("docker", "列出镜像失败: " + e.getMessage());
        }
    }

    /**
     * 拉取镜像
     * @param imageName 镜像名称
     * @param tag 镜像标签
     */
    public void pullImage(String imageName, String tag) {
        try {
            String fullImageName = StringUtils.isNotBlank(tag) ? imageName + ":" + tag : imageName;
            dockerClient.pullImageCmd(fullImageName).exec(new PullImageResultCallback()).awaitCompletion();
            log.info("镜像拉取成功: {}", fullImageName);
        } catch (Exception e) {
            log.error("拉取镜像失败: {}", imageName, e);
            throw new RemoteExecutionException("docker", "拉取镜像失败: " + e.getMessage());
        }
    }

    /**
     * 删除镜像
     * @param imageId 镜像ID
     * @param force 是否强制删除
     */
    public void removeImage(String imageId, boolean force) {
        try {
            dockerClient.removeImageCmd(imageId)
                    .withForce(force)
                    .exec();
            log.info("镜像删除成功: {}", imageId);
        } catch (Exception e) {
            log.error("删除镜像失败: {}", imageId, e);
            throw new RemoteExecutionException("docker", "删除镜像失败: " + e.getMessage());
        }
    }

    /**
     * 获取镜像详细信息
     * @param imageId 镜像ID
     * @return 镜像信息
     */
    public InspectImageResponse inspectImage(String imageId) {
        try {
            return dockerClient.inspectImageCmd(imageId).exec();
        } catch (Exception e) {
            log.error("获取镜像信息失败: {}", imageId, e);
            throw new RemoteExecutionException("docker", "获取镜像信息失败: " + e.getMessage());
        }
    }

    /**
     * 标记镜像
     * @param imageId 镜像ID
     * @param repository 仓库名
     * @param tag 标签
     */
    public void tagImage(String imageId, String repository, String tag) {
        try {
            dockerClient.tagImageCmd(imageId, repository, tag).exec();
            log.info("镜像标记成功: {} -> {}:{}", imageId, repository, tag);
        } catch (Exception e) {
            log.error("标记镜像失败: {}", imageId, e);
            throw new RemoteExecutionException("docker", "标记镜像失败: " + e.getMessage());
        }
    }

    /**
     * 根据镜像名称模糊检索镜像
     * @param imageName 镜像名称（支持模糊匹配）
     * @return 匹配的镜像列表
     */
    public List<Image> searchImagesByName(String imageName) {
        try {
            List<Image> allImages = dockerClient.listImagesCmd().exec();
            return allImages.stream()
                    .filter(image -> {
                        if (image.getRepoTags() == null) {
                            return false;
                        }
                        return Arrays.stream(image.getRepoTags())
                                .anyMatch(tag -> tag.toLowerCase().contains(imageName.toLowerCase()));
                    })
                    .collect(java.util.stream.Collectors.toList());
        } catch (Exception e) {
            log.error("搜索镜像失败: {}", imageName, e);
            throw new RemoteExecutionException("docker", "搜索镜像失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定镜像的所有版本标签
     * @param repositoryName 仓库名称（不包含标签）
     * @return 该镜像的所有版本标签列表
     */
    public List<String> getImageVersions(String repositoryName) {
        try {
            List<Image> allImages = dockerClient.listImagesCmd().exec();
            return allImages.stream()
                    .filter(image -> image.getRepoTags() != null)
                    .flatMap(image -> Arrays.stream(image.getRepoTags()))
                    .filter(tag -> tag.startsWith(repositoryName + ":"))
                    .map(tag -> tag.substring(repositoryName.length() + 1)) // 提取标签部分
                    .distinct()
                    .sorted()
                    .collect(java.util.stream.Collectors.toList());
        } catch (Exception e) {
            log.error("获取镜像版本失败: {}", repositoryName, e);
            throw new RemoteExecutionException("docker", "获取镜像版本失败: " + e.getMessage());
        }
    }

    /**
     * 获取镜像的详细版本信息（包含镜像ID、大小、创建时间等）
     * @param repositoryName 仓库名称
     * @return 镜像版本详细信息列表
     */
    public List<ImageVersionInfo> getImageVersionDetails(String repositoryName) {
        try {
            List<Image> allImages = dockerClient.listImagesCmd().exec();
            return allImages.stream()
                    .filter(image -> image.getRepoTags() != null)
                    .filter(image -> Arrays.stream(image.getRepoTags())
                            .anyMatch(tag -> tag.startsWith(repositoryName + ":")))
                    .map(image -> {
                        String tag = Arrays.stream(image.getRepoTags())
                                .filter(t -> t.startsWith(repositoryName + ":"))
                                .findFirst()
                                .orElse("");
                        String version = tag.substring(repositoryName.length() + 1);
                        return new ImageVersionInfo(
                                image.getId(),
                                repositoryName,
                                version,
                                tag,
                                image.getSize(),
                                image.getCreated()
                        );
                    })
                    .sorted((a, b) -> Long.compare(b.getCreated(), a.getCreated())) // 按创建时间倒序
                    .collect(java.util.stream.Collectors.toList());
        } catch (Exception e) {
            log.error("获取镜像版本详情失败: {}", repositoryName, e);
            throw new RemoteExecutionException("docker", "获取镜像版本详情失败: " + e.getMessage());
        }
    }

    /**
     * 镜像版本信息类
     */
    public static class ImageVersionInfo {
        private final String imageId;
        private final String repository;
        private final String version;
        private final String fullTag;
        private final Long size;
        private final Long created;

        public ImageVersionInfo(String imageId, String repository, String version,
                              String fullTag, Long size, Long created) {
            this.imageId = imageId;
            this.repository = repository;
            this.version = version;
            this.fullTag = fullTag;
            this.size = size;
            this.created = created;
        }

        public String getImageId() { return imageId; }
        public String getRepository() { return repository; }
        public String getVersion() { return version; }
        public String getFullTag() { return fullTag; }
        public Long getSize() { return size; }
        public Long getCreated() { return created; }

        public String getSizeFormatted() {
            if (size == null) return "未知";
            if (size < 1024) return size + " B";
            if (size < 1024 * 1024) return String.format("%.1f KB", size / 1024.0);
            if (size < 1024 * 1024 * 1024) return String.format("%.1f MB", size / (1024.0 * 1024));
            return String.format("%.1f GB", size / (1024.0 * 1024 * 1024));
        }

        @Override
        public String toString() {
            return String.format("%s:%s (ID: %s, Size: %s)",
                    repository, version,
                    imageId != null ? imageId.substring(0, Math.min(12, imageId.length())) : "unknown",
                    getSizeFormatted());
        }
    }

    // ==================== 网络管理方法 ====================

    /**
     * 列出所有网络
     * @return 网络列表
     */
    public List<Network> listNetworks() {
        try {
            return dockerClient.listNetworksCmd().exec();
        } catch (Exception e) {
            log.error("列出网络失败", e);
            throw new RemoteExecutionException("docker", "列出网络失败: " + e.getMessage());
        }
    }

    /**
     * 创建网络
     * @param networkName 网络名称
     * @param driver 网络驱动
     * @return 网络创建响应
     */
    public CreateNetworkResponse createNetwork(String networkName, String driver) {
        try {
            CreateNetworkCmd cmd = dockerClient.createNetworkCmd()
                    .withName(networkName);
            if (StringUtils.isNotBlank(driver)) {
                cmd.withDriver(driver);
            }
            CreateNetworkResponse response = cmd.exec();
            log.info("网络创建成功: {}", networkName);
            return response;
        } catch (Exception e) {
            log.error("创建网络失败: {}", networkName, e);
            throw new RemoteExecutionException("docker", "创建网络失败: " + e.getMessage());
        }
    }

    /**
     * 删除网络
     * @param networkId 网络ID
     */
    public void removeNetwork(String networkId) {
        try {
            dockerClient.removeNetworkCmd(networkId).exec();
            log.info("网络删除成功: {}", networkId);
        } catch (Exception e) {
            log.error("删除网络失败: {}", networkId, e);
            throw new RemoteExecutionException("docker", "删除网络失败: " + e.getMessage());
        }
    }

    // ==================== 卷管理方法 ====================

    /**
     * 列出所有卷
     * @return 卷列表响应
     */
    public ListVolumesResponse listVolumes() {
        try {
            return dockerClient.listVolumesCmd().exec();
        } catch (Exception e) {
            log.error("列出卷失败", e);
            throw new RemoteExecutionException("docker", "列出卷失败: " + e.getMessage());
        }
    }

    /**
     * 创建卷
     * @param volumeName 卷名称
     * @param driver 卷驱动
     * @return 卷创建响应
     */
    public CreateVolumeResponse createVolume(String volumeName, String driver) {
        try {
            CreateVolumeCmd cmd = dockerClient.createVolumeCmd()
                    .withName(volumeName);
            if (StringUtils.isNotBlank(driver)) {
                cmd.withDriver(driver);
            }
            CreateVolumeResponse response = cmd.exec();
            log.info("卷创建成功: {}", volumeName);
            return response;
        } catch (Exception e) {
            log.error("创建卷失败: {}", volumeName, e);
            throw new RemoteExecutionException("docker", "创建卷失败: " + e.getMessage());
        }
    }

    /**
     * 删除卷
     * @param volumeName 卷名称
     * @param force 是否强制删除
     */
    public void removeVolume(String volumeName, boolean force) {
        try {
            dockerClient.removeVolumeCmd(volumeName)
                    .withForce(force)
                    .exec();
            log.info("卷删除成功: {}", volumeName);
        } catch (Exception e) {
            log.error("删除卷失败: {}", volumeName, e);
            throw new RemoteExecutionException("docker", "删除卷失败: " + e.getMessage());
        }
    }

    @Override
    public void close() throws IOException {
        try {
            if (dockerClient != null) {
                dockerClient.close();
                log.info("Docker 客户端已关闭");
            }
            if (httpClient != null) {
                httpClient.close();
                log.info("Docker HTTP 客户端已关闭");
            }
        } catch (Exception e) {
            log.error("关闭 Docker 客户端失败", e);
            throw new IOException("关闭 Docker 客户端失败", e);
        }
    }
}
