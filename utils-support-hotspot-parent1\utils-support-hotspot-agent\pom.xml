<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.chua</groupId>
        <artifactId>utils-support-hotspot-parent</artifactId>
        <version>4.0.0.26</version>
    </parent>

    <artifactId>utils-support-hotspot-agent</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <premain>com.chua.attach.hotspot.support.Agent</premain>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.chua</groupId>
            <artifactId>utils-support-hotspot-core</artifactId>
            <version>4.0.0.26</version>
        </dependency>

        <dependency>
            <groupId>com.chua</groupId>
            <artifactId>utils-support-hotspot-netty</artifactId>
            <version>4.0.0.26</version>
        </dependency>

        <dependency>
            <groupId>com.chua</groupId>
            <artifactId>utils-support-hotspot-hotswap</artifactId>
            <version>4.0.0.26</version>
        </dependency>

        <dependency>
            <groupId>com.chua</groupId>
            <artifactId>utils-support-hotspot-jackson</artifactId>
            <version>4.0.0.26</version>
        </dependency>
        <dependency>
            <groupId>com.chua</groupId>
            <artifactId>utils-support-hotspot-redis</artifactId>
            <version>4.0.0.26</version>
        </dependency>

        <dependency>
            <groupId>com.chua</groupId>
            <artifactId>utils-support-hotspot-fastjson</artifactId>
            <version>4.0.0.26</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.chua</groupId>-->
<!--            <artifactId>utils-support-hotspot-spring</artifactId>-->
<!--            <version>4.0.0.26</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.chua</groupId>
            <artifactId>utils-support-hotspot-p6spy</artifactId>
            <version>4.0.0.26</version>
        </dependency>

        <dependency>
            <groupId>com.chua</groupId>
            <artifactId>utils-support-hotspot-logger</artifactId>
            <version>4.0.0.26</version>
        </dependency>

        <dependency>
            <groupId>com.chua</groupId>
            <artifactId>utils-support-hotspot-httpclient</artifactId>
            <version>4.0.0.26</version>
        </dependency>

        <dependency>
            <groupId>com.chua</groupId>
            <artifactId>utils-support-hotspot-dubbo3x</artifactId>
            <version>4.0.0.26</version>
        </dependency>

        <dependency>
            <groupId>com.chua</groupId>
            <artifactId>utils-support-hotspot-tomcat</artifactId>
            <version>4.0.0.26</version>
        </dependency>
        <dependency>
            <groupId>com.chua</groupId>
            <artifactId>utils-support-hotspot-undertow</artifactId>
            <version>4.0.0.26</version>
        </dependency>

        <dependency>
            <groupId>com.chua</groupId>
            <artifactId>utils-support-hotspot-mysql</artifactId>
            <version>4.0.0.26</version>
        </dependency>

        <dependency>
            <groupId>com.chua</groupId>
            <artifactId>utils-support-hotspot-mybatis</artifactId>
            <version>4.0.0.26</version>
        </dependency>

        <dependency>
            <groupId>com.chua</groupId>
            <artifactId>utils-support-hotspot-ui</artifactId>
            <version>4.0.0.26</version>
        </dependency>

        <dependency>
            <groupId>com.chua</groupId>
            <artifactId>utils-support-hotspot-okhttp</artifactId>
            <version>4.0.0.26</version>
        </dependency>

        <dependency>
            <groupId>com.chua</groupId>
            <artifactId>utils-support-hotspot-rabbit</artifactId>
            <version>4.0.0.26</version>
        </dependency>

        <dependency>
            <groupId>com.chua</groupId>
            <artifactId>utils-support-hotspot-zookeeper</artifactId>
            <version>4.0.0.26</version>
        </dependency>

        <dependency>
            <groupId>com.chua</groupId>
            <artifactId>utils-support-hotspot-kafka</artifactId>
            <version>4.0.0.26</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>2.8</version>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}</outputDirectory>
                            <includeScope>runtime</includeScope>
                            <excludeScope>provider</excludeScope>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>3.0.2</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.2.4</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <artifactSet>
                        <includes>
                            <dependency>org.benf:cfr:0.152</dependency>
                            <dependency>org.java-websocket:Java-WebSocket:1.5.7</dependency>
                        </includes>
                    </artifactSet>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>2.6</version>
                <configuration>
                    <descriptorRefs>
                        <descriptorRef>jar-with-dependencies</descriptorRef>
                    </descriptorRefs>
                    <finalName>${project.artifactId}-${project.version}</finalName>
                    <appendAssemblyId>false</appendAssemblyId>
                    <archive>
                        <manifestEntries>
                            <Premain-Class>${premain}</Premain-Class>
                            <Agent-Class>${premain}</Agent-Class>
                            <Can-Retransform-Classes>true</Can-Retransform-Classes>
                            <Can-Redefine-Classes>true</Can-Redefine-Classes>
                            <Build-By>CH</Build-By>
                        </manifestEntries>
                    </archive>
                </configuration>
                <executions>
                    <execution>
                        <id>assemble-all</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>