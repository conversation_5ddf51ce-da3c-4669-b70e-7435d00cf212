package com.chua.attach.hotspot.core.support.plugin;

import com.chua.attach.hotspot.core.support.constant.Constant;
import com.chua.attach.hotspot.core.support.log.LogFactory;
import com.chua.attach.hotspot.core.support.span.Span;
import net.bytebuddy.agent.builder.AgentBuilder;
import net.bytebuddy.description.type.TypeDescription;
import net.bytebuddy.dynamic.DynamicType;
import net.bytebuddy.matcher.ElementMatcher;

import java.io.File;
import java.lang.reflect.Method;

/**
 * 插件
 *
 * <AUTHOR>
 */
public interface Plugin extends Constant {

    LogFactory logFactory = LogFactory.getInstance();
    /**
     * 名称
     *
     * @return {@link String}
     */
    String name();


    /**
     * 打印
     *
     * @return boolean
     */
    default boolean print() {
        return true;
    }
    /**
     * 初始化
     */
    void init();

    /**
     * 初始化完成
     */
    default void initComplete() {
        if(!print() || null == name()) {
            return;
        }
        logFactory.info("{} Completes registration", name().toLowerCase());
    }
    /**
     * 匹配重建
     *
     * @param type 类型
     * @return boolean
     */
    default boolean matchRebuild(Class<?> type) {
        return false;
    }

    /**
     * 重建
     *
     * @param type  类型
     * @param bytes
     */
    default void rebase(Class<?> type, byte[] bytes) {

    }
    /**
     * 编译器
     *
     * @param builder 编译器
     * @return ReceiverTypeDefinition
     */
    DynamicType.Builder.MethodDefinition.ReceiverTypeDefinition<?> transform(DynamicType.Builder<?> builder);

    /**
     * 编译器
     *
     * @param transform 编译器
     * @return Extendable
     */
    default AgentBuilder.Identified.Extendable transforms(AgentBuilder.Identified.Extendable transform) {
        return transform;
    }

    /**
     * 类型
     *
     * @return 类型
     */
    ElementMatcher<? super TypeDescription> type();

    /**
     * 链路
     *
     * @param target
     * @param method 方法
     * @param args   参数
     * @param span   链路
     */
    static void doRefreshSpan(Object target, Method method, Object[] args, Span span) {
        if(null == method) {
            return;
        }
        String className = target.getClass().getName();
        String methodName = method.getName();
        span.setFrom(className + "." + methodName);
        span.setMessage("链路追踪(MQ)：" + className + "." + methodName);
        span.setMethod(className + "." + methodName);
        span.setArgs(args);
        span.setMethod(methodName);
        span.setType(className);
        logFactory.debug(span.getMessage());

    }

    /**
     * 完成
     */
    default void finish() {

    }

    /**
     * 重定义xml
     *
     * @param file 文件
     */
    default void rebaseXml(File file) {

    }
}
