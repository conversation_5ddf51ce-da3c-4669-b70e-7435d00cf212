package com.chua.common.support.protocol;

import com.chua.common.support.collection.Options;
import com.chua.common.support.crypto.Codec;
import com.chua.common.support.net.NetAddress;
import com.chua.common.support.utils.IdUtils;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * 客户端配置
 * <AUTHOR>
 * @since 2024/6/28
 */
@Accessors(fluent = true)
@SuperBuilder
@Getter
@Setter
public class ClientSetting {

    /**
     * url
     */
    private String url;
    /**
     * 协议
     */
    private String protocol;

    /**
     * 存储远程服务的请求路径。
     * 该路径用于指定请求的URL路径，用于标识请求的服务端处理逻辑。
     */
    private String path;
    /**
     * 存储远程服务的主机名或IP地址。
     * 该主机名或IP地址用于标识远程服务的位置，通过该主机名或IP地址可以访问到服务提供的功能。
     */
    private String host;

    /**
     * 存储远程服务的端口号。
     * 该端口号用于标识远程服务的位置，通过该端口号可以访问到服务提供的功能。
     */
    private Integer port;
    /**
     * 存储远程服务的用户名。
     * 该用户名用于在访问远程服务时进行身份验证，确保只有授权的用户可以访问服务。
     */
    private String username;

    /**
     * 存储远程服务的密码。
     * 该密码与用户名配合使用，用于在访问远程服务时进行身份验证，确保只有授权的用户可以访问服务。
     */
    private String password;


    /**
     * 客户端ID
     */
    @Builder.Default
    private String clientId = IdUtils.createUlid();

    /**
     * 存储远程服务的端点。
     * 该端点用于标识远程服务的具体功能，通过该端点可以访问到服务提供的功能。
     */
    private String endpoint;

    /**
     * 代理
     */
    private String proxyHost;

    /**
     * 代理端口
     */
    private Integer proxyPort;

    /**
     * 最大连接数
     */
    @Builder.Default
    private int maxTotal = 100;
    /**
     * 数据库
     */
    @Builder.Default
    private String database = "__default";

    /**
     * 存储远程服务的连接超时时间。
     * 该超时时间用于设置连接远程服务时的超时时间，如果连接超时，则认为服务不可用。
     */
    @Builder.Default
    private int connectTimeoutMillis = 30_000;

    /**
     * 存储远程服务的会话超时时间。
     * 该超时时间用于设置会话远程服务时的超时时间，如果会话超时，则认为服务不可用。
     */
    @Builder.Default
    private int sessionTimeoutMillis = 0;

    /**
     * 存储远程服务的超时时间。
     * 该超时时间用于设置会话远程服务时的超时时间，如果会话超时，则认为服务不可用。
     */
    @Builder.Default
    private int timeoutMillis = 60_000;
    /**
     * 心跳
     */
    private boolean heartbeat;

    /**
     * 心跳间隔(s)
     */
    @Builder.Default
    private int heartbeatInterval = 10;
    /**
     * 编解码
     */
    private Codec codec;

    /**
     * 额外配置
     */
    @Builder.Default
    private Options options = new Options();

    public int getPort(int defaultPort) {
        return null == port ? defaultPort : port;
    }
    /**
     * 主机
     * @return 主机
     */

    public String getFullUrl() {
        if(null != url) {
            return url;
        }

        if(null != protocol) {
            return protocol + "://" + host + ":" + port;
        }

        return host + ":" + port;
    }

    public String getHost() {
        return host != null ?  host : NetAddress.of(url).getHost();
    }

    public Integer getPort() {
        return port != null ?  port : NetAddress.of(url).getPort();
    }

    public String getUsername() {
        return username != null ?  username : NetAddress.of(url).getUsername();
    }

    public String getPassword() {
        return password != null ?  password : NetAddress.of(url).getPassword();
    }

    public String getProtocol() {
        return protocol  != null ?  protocol : NetAddress.of(url).getProtocol();
    }


}
