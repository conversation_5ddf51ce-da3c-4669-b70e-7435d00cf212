package com.chua.attach.hotspot.core.support.link;

import com.chua.attach.hotspot.core.support.span.Span;
import com.chua.attach.hotspot.core.support.utils.ClassUtils;
import com.chua.attach.hotspot.core.support.utils.StringUtils;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class LinkResolverFactory {

    static final LinkResolverFactory INSTANCE = new LinkResolverFactory();
    private List<LinkResolver> linkResolvers = new LinkedList<>();
    private final List<LinkResolver> ignored = new LinkedList<>();
    LinkResolverFactory() {
        List<Class<?>> classes1 = ClassUtils.getClasses("com.chua.attach.hotspot.core.support.plugin");
        for (Class<?> aClass : classes1) {
            if(!LinkResolver.class.isAssignableFrom(aClass)) {
                continue;
            }
            try {
                LinkResolver o = (LinkResolver) aClass.newInstance();
                linkResolvers.add(o);
            } catch (Throwable ignored) {
            }
        }
    }

    public static LinkResolverFactory getInstance() {
        return INSTANCE;
    }

    /**
     * 获取链接id
     *
     * @param args args
     * @return {@link String}
     */
    public String getLinkId(Object[] args) {
        if(null == args ) {
            return null;
        }

        for (LinkResolver linkResolver : linkResolvers) {
            if(ignored.contains(linkResolver)) {
                continue;
            }
            String linkId = null;
            try {
                linkId = linkResolver.getLinkId(args);
            } catch (Throwable e) {
                ignored.add(linkResolver);
                continue;
            }
            if(StringUtils.isBlank(linkId)) {
                continue;
            }

            return linkId;
        }
        return null;
    }
    /**
     * 获取链接id
     *
     * @param args args
     * @return {@link String}
     */
    public String getLinkParentId(Object[] args) {
        if(null == args ) {
            return null;
        }

        for (LinkResolver linkResolver : linkResolvers) {
            if(ignored.contains(linkResolver)) {
                continue;
            }
            String linkId = null;
            try {
                linkId = linkResolver.getLinkParentId(args);
            } catch (Throwable e) {
                ignored.add(linkResolver);
                continue;
            }
            if(StringUtils.isBlank(linkId)) {
                continue;
            }

            return linkId;
        }
        return null;
    }

    public void sendResponse(List<Span> spans, Object response) {
        for (LinkResolver linkResolver : linkResolvers) {
            linkResolver.sendResponse(spans, response);
        }
    }
}
