package com.chua.common.support.protocol.client;

import com.chua.common.support.protocol.session.Session;

/**
 * Client接口定义了客户端的连接行为。
 *
 * 该接口的实现负责建立和管理与服务端的连接。通过实现此接口，
 * 类可以提供特定于服务端连接的逻辑，包括连接的建立、维护和断开。
 *
 * <AUTHOR>
 * @since 2024/6/28
 */
public interface Client extends AutoCloseable{


    /**
     * 断开与服务端的连接。
     * <p>
     * 此方法应该关闭与服务端的连接，并释放所有相关的资源。
     * <p>
     * 方法没有返回值，因为其主要目的是关闭连接，而不是返回连接状态。
     * 如果需要检查连接是否成功关闭，可以在方法实现中使用日志记录或异常处理来提供反馈。
     */
    default void disconnect() throws Exception {
        close();
    }

    /**
     * 检查当前客户端是否已连接。
     * <p>
     * 此方法返回一个布尔值，表示当前客户端是否已连接。
     *
     * @return true 表示已连接，false 表示未连接。
     */
    boolean isConnected();
    /**
     * 创建会话
     * @param name 会话名称
     * @return 会话
     */
    Session createSession(String name);

    /**
     * 获取会话
     * @return 会话
     */
    Session getSession();
    /**
     * 建立与服务端的连接。
     *
     * 此方法的实现应该包括所有必要的步骤，以确保客户端能够成功连接到服务端。
     * 这可能包括配置连接参数、处理认证和授权、管理连接状态等。
     *
     * 方法没有返回值，因为其主要目的是初始化和建立连接，而不是返回连接状态。
     * 如果需要检查连接是否成功建立，可以在方法实现中使用日志记录或异常处理来提供反馈。
     */
    void connect();

    /**
     * 关闭与服务端的连接。
     *
     * 此方法的实现应该包括所有必要的步骤，以确保客户端能够成功断开与服务端的连接。
     * 这可能包括清理资源、关闭连接等。
     *
     * 方法没有返回值，因为其主要目的是关闭连接，而不是返回连接状态。
     * 如果需要检查连接是否成功关闭，可以在方法实现中使用日志记录或异常处理来提供反馈。
     */
    void closeSession(String exec);
}

