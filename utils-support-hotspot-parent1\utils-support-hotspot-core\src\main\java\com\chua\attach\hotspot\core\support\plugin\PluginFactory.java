package com.chua.attach.hotspot.core.support.plugin;

import com.chua.attach.hotspot.core.support.environment.EnvironmentFactory;
import com.chua.attach.hotspot.core.support.log.LogFactory;
import com.chua.attach.hotspot.core.support.utils.ClassUtils;

import java.io.File;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class PluginFactory {

    static final PluginFactory INSTANCE = new PluginFactory();

    final LogFactory logFactory = LogFactory.getInstance();
    final EnvironmentFactory environmentFactory = EnvironmentFactory.getInstance();
    final List<String> denyPlugin = new LinkedList<>();

    final Map<String, Plugin> pluginMap = new ConcurrentHashMap<>();
    final List<Plugin> pluginList = new LinkedList<>();
    private PluginFactory() {
    }

    /**
     * 获取实例
     *
     * @return {@link PluginFactory}
     */
    public static PluginFactory getInstance() {
        return INSTANCE;
    }

    public void init() {
        logFactory.info("初始化插件");
        initialDenyList();
        List<Class<?>> classes = ClassUtils.getClasses("com.chua.attach.hotspot.core.support.plugin");
        for (Class<?> aClass : classes) {
            if(aClass.isInterface()) {
                continue;
            }

            try {
                Plugin newInstance = (Plugin) aClass.newInstance();
                pluginMap.put(newInstance.name(), newInstance);
                pluginList.add(newInstance);
            } catch (Exception ignored) {
            }
        }

        Set<String> collect = pluginMap.entrySet().stream().filter(it -> it.getValue().print())
                .map(Map.Entry::getKey).limit(5).collect(Collectors.toSet());
        logFactory.info("共发现: {} ({})", collect,  pluginMap.size());
    }

    private void initialDenyList() {
        String denyPlugin1 = environmentFactory.getString("denyPlugin", "");
        String[] split = denyPlugin1.split(",");
        for (String s : split) {
            denyPlugin.add(s.trim());
        }
    }

    /**
     * 列表
     *
     * @return {@link Collection}<{@link Plugin}>
     */
    public Collection<Plugin> toList() {
        return pluginList;
    }

    /**
     * 是否通过
     *
     * @param name 名称
     * @return boolean
     */
    public boolean isPass(String name) {
        return !denyPlugin.contains(name);
    }

    /**
     * 重定义
     *
     * @param type  类型
     * @param bytes
     */
    public void rebase(Class<?> type, byte[] bytes) {
        //针对框架重构定义
        for (Plugin plugin : pluginList) {
            plugin.rebase(type, bytes);
        }
    }

    /**
     * 完成
     */
    public void finish() {
        for (Plugin plugin : pluginList) {
            plugin.finish();
        }
    }

    /**
     * 重定义xml
     *
     * @param file 文件
     */
    public void rebaseXml(File file) {
        for (Plugin plugin : pluginList) {
            plugin.rebaseXml(file);
        }
    }
}
