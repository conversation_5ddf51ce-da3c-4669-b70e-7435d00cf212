package com.chua.attach.hotspot.core.support.json.spi;

import com.chua.attach.hotspot.core.support.json.parser.ParserConfig;
import com.chua.attach.hotspot.core.support.json.parser.deserializer.ObjectDeserializer;
import com.chua.attach.hotspot.core.support.json.serializer.ObjectSerializer;
import com.chua.attach.hotspot.core.support.json.serializer.SerializeConfig;

public interface Module {
    ObjectDeserializer createDeserializer(ParserConfig config, Class type);

    ObjectSerializer createSerializer(SerializeConfig config, Class type);
}
