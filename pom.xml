<?xml version="1.0" encoding="UTF-8"?>
<!--
    utils-support-parent-starter 项目主POM文件

    这是一个多模块的Maven父项目，提供了丰富的Java工具包集合
    主要功能包括：云服务集成、媒体处理、数据库操作、深度学习、监控等

    项目特点：
    - 基于Java 21开发
    - 采用SPI扩展机制，支持可插拔组件
    - 提供统一的API接口
    - 支持多种云服务提供商
    - 集成主流中间件和框架

    作者：CH
    版本：********
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- 项目基本信息 -->
    <groupId>com.chua</groupId>
    <artifactId>utils-support-parent-starter</artifactId>
    <packaging>pom</packaging>
    <version>********</version>

    <!-- 子模块列表 -->
    <modules>
        <!-- 基础工具包：提供核心功能如消息总线、加解密、数据库查询、全文检索等 -->
        <module>utils-support-common-starter</module>

        <!-- 云服务集成模块：支持阿里云、腾讯云、百度云、AWS等多家云服务商 -->
        <module>utils-support-cloud-parent</module>

        <!-- 媒体处理模块：图片处理、文档转换、视频处理、二维码生成等 -->
        <module>utils-support-media-parent</module>

        <!-- 软件集成模块：Elasticsearch、MongoDB、Redis、Kafka等中间件集成 -->
        <module>utils-support-software-parent</module>

        <!-- 衍生工具模块：各种扩展工具和第三方库集成 -->
        <module>utils-support-derive-parent</module>

        <!-- Web资源模块：前端资源和WebJars支持 -->
        <module>utils-support-webjars-parent</module>

        <!-- 深度学习模块：PyTorch、TensorFlow、OpenCV等AI框架支持（暂时注释） -->
        <!--<module>utils-support-deeplearning-parent</module>-->

        <!-- Spring集成模块：Spring框架相关工具和扩展 -->
        <module>utils-support-spring-starter</module>

        <!-- 资源处理模块：文件资源管理和处理 -->
        <module>utils-support-resource-starter</module>

        <!-- 热点监控模块：应用性能监控和热点分析 -->
        <module>utils-support-hotspot-parent</module>

        <!-- 以下是特定功能的独立模块 -->
        <!-- SSH连接工具 -->
        <module>utils-support-derive-parent/utils-support-sshj-starter</module>
        <!-- 串口通信工具 -->
        <module>utils-support-derive-parent/utils-support-com-starter</module>
        <!-- 测试工具集 -->
        <module>utils-support-derive-parent/utils-support-test-starter</module>
        <!-- 浏览器自动化工具（Playwright） -->
        <module>utils-support-derive-parent/utils-support-playwright-starter</module>
        <!-- 命令行工具 -->
        <module>utils-support-derive-parent/utils-support-cli-stater</module>

        <!-- 重复的热点监控模块（已注释） -->
        <!--<module>utils-support-hotspot-parent</module>-->
    </modules>

    <!-- JVM参数配置提示：启用IPv4优先 -->
    <!-- 使用方式：mvn clean install -Djava.net.preferIPv4Stack=true -->

    <!-- 项目属性配置 -->
    <properties>
        <!-- 项目编码设置 -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <!-- Java版本配置：使用Java 21 LTS版本 -->
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <java.version>21</java.version>

        <!-- 项目版本管理 -->
        <utils.version>********</utils.version>

        <!-- 核心依赖版本管理 -->
        <!-- Apache Calcite：SQL解析和查询优化引擎 -->
        <calcite.version>1.34.0</calcite.version>

        <!-- 计算机视觉和深度学习相关版本 -->
        <!-- OpenCV：计算机视觉库 -->
        <opencv.version>4.9.0-0</opencv.version>
        <!-- JavaCV：Java计算机视觉库 -->
        <javacv.version>1.5.9</javacv.version>
        <!-- JavaCPP：Java到C++的桥接库 -->
        <javacpp.version>1.5.9</javacpp.version>
        <!-- FFmpeg：音视频处理库 -->
        <javacv.ffmpeg.version>6.0-1.5.9</javacv.ffmpeg.version>
        <!-- OpenCV for JavaCV -->
        <javacv.opencv.version>4.7.0-1.5.9</javacv.opencv.version>

        <!-- 跨平台支持：定义不同操作系统和架构的平台标识 -->
        <!-- macOS x86_64架构 -->
        <javacpp.platform.macosx-x86_64>macosx-x86_64</javacpp.platform.macosx-x86_64>
        <!-- Linux x86架构 -->
        <javacpp.platform.linux-x86>linux-x86</javacpp.platform.linux-x86>
        <!-- Linux x86_64架构 -->
        <javacpp.platform.linux-x86_64>linux-x86_64</javacpp.platform.linux-x86_64>
        <!-- Windows x86架构 -->
        <javacpp.platform.windows-x86>windows-x86</javacpp.platform.windows-x86>
        <!-- Windows x86_64架构 -->
        <javacpp.platform.windows-x86_64>windows-x86_64</javacpp.platform.windows-x86_64>
    </properties>


    <!-- 项目直接依赖：所有子模块都会继承这些依赖 -->
    <dependencies>
        <!-- Lombok：Java代码简化工具，提供注解自动生成getter/setter等方法 -->
        <!-- scope=provided表示编译时需要，运行时不需要打包 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.34</version>
            <scope>provided</scope>
        </dependency>

        <!-- IntelliJ IDEA注解：提供@Nullable、@NotNull等代码质量注解 -->
        <!-- 帮助IDE进行静态代码分析和警告提示 -->
        <dependency>
            <groupId>com.intellij</groupId>
            <artifactId>annotations</artifactId>
            <version>12.0</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <!-- 依赖版本管理：统一管理子模块中使用的依赖版本 -->
    <!-- 子模块可以直接引用这里定义的依赖，无需指定版本号 -->
    <dependencyManagement>
        <dependencies>
            <!-- 基础工具包依赖管理 -->
            <dependency>
                <groupId>com.chua</groupId>
                <artifactId>utils-support-common-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!-- 构建配置 -->
    <build>
        <plugins>
            <!-- Maven编译插件：配置Java编译相关参数 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.13.0</version>
                <configuration>
                    <!-- 源代码兼容性：Java 21 -->
                    <source>21</source>
                    <!-- 目标字节码版本：Java 21 -->
                    <target>21</target>
                    <!-- 发布版本：Java 21（推荐使用release而不是source/target） -->
                    <release>21</release>

                    <!-- 注解处理器路径配置 -->
                    <!-- 确保Lombok注解处理器在编译时可用 -->
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.34</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!-- 分发管理：配置Maven仓库发布地址 -->
    <distributionManagement>
        <!-- 正式版本发布仓库 -->
        <repository>
            <id>boren001</id>
            <!-- 仓库名称（注意：原文件使用了非标准的<n>标签，标准应该使用<name>） -->
            <name>boren001</name>
            <!-- 私有Maven仓库地址：用于发布release版本 -->
            <url>http://192.168.110.100:8081/repository/maven-releases/</url>
        </repository>

        <!-- 快照版本发布仓库 -->
        <snapshotRepository>
            <id>boren001</id>
            <!-- 仓库名称（注意：原文件使用了非标准的<n>标签，标准应该使用<name>） -->
            <name>boren001</name>
            <!-- 私有Maven仓库地址：用于发布snapshot版本 -->
            <!-- 注意：这里快照仓库和正式仓库使用了相同的URL，通常应该使用不同的URL -->
            <url>http://192.168.110.100:8081/repository/maven-releases/</url>
        </snapshotRepository>
    </distributionManagement>

</project>