package com.chua.attach.hotspot.core.support.log;

import com.chua.attach.hotspot.core.support.environment.EnvironmentFactory;
import com.chua.attach.hotspot.core.support.utils.DateUtils;
import com.chua.attach.hotspot.core.support.utils.StringUtils;

import java.util.logging.Level;

/**
 * LogFactory
 *
 * <AUTHOR>
 */
public class LogFactory {


    static final LogFactory INSTANCE = new LogFactory();
    private Level level;

    public static LogFactory getInstance() {
        return INSTANCE;
    }

    private LogFactory() {
    }

    public void init() {
        EnvironmentFactory environmentFactory = EnvironmentFactory.getInstance();
        this.level = Level.parse(environmentFactory.getString("log", Level.INFO.getName()).toUpperCase());
        info("完成日志初始化");
    }


    /**
     * 信息
     *
     * @param message 消息
     * @param args    args
     */
    public void error(String message, Object... args) {
        if(level.intValue() <= Level.SEVERE.intValue()) {
            String name = Thread.currentThread().getName();
            message = "["+ DateUtils.current() + "] [INFO ] ["+ name +"] [HotSpot] " + message;
            log(StringUtils.format(message, args));
        }
    }


    /**
     * 信息
     *
     * @param message 消息
     * @param args    args
     */
    public void info(String message, Object... args) {
        if(level.intValue() <= Level.INFO.intValue()) {
            String name = Thread.currentThread().getName();
            message = "["+ DateUtils.current() + "] [INFO ] ["+ name +"] [HotSpot] " + message;
            log(StringUtils.format(message, args));
        }
    }

    /**
     * 信息
     *
     * @param message 消息
     * @param args    args
     */
    public void debug(String message, Object... args) {
        if(level.intValue() <= Level.FINE.intValue()) {
            String name = Thread.currentThread().getName();
            message = "["+ DateUtils.current() + "] [INFO ] ["+ name +"] [HotSpot] " + message;
            log(StringUtils.format(message, args));
        }
    }
    /**
     * 信息
     *
     * @param message 消息
     * @param args    args
     */
    public void trace(String message, Object... args) {
        if(level.intValue() <= Level.ALL.intValue()) {
            String name = Thread.currentThread().getName();
            message = "["+ DateUtils.current() + "] [INFO ] ["+ name +"] [HotSpot] " + message;
            log(StringUtils.format(message, args));
        }
    }


    private void log(String message) {
        System.out.println(message);
    }
}
