package com.chua.common.support.lang.engine.expression.resolver;

import com.chua.common.support.lang.engine.expression.node.LogicalExpression;

/**
 * 逻辑表达式处理器测试类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/04/09
 */
public class DefaultLogicalExpressionHandlerTest {

    public static void main(String[] args) {
        testSimpleExpression();
        testComparisonExpression();
        testAndExpression();
        testOrExpression();
        testNestedExpression();
        testRangeExpression();
        testLikeExpression();
        testInExpression();
    }

    /**
     * 测试简单等于表达式
     */
    private static void testSimpleExpression() {
        System.out.println("====== 测试简单等于表达式 ======");
        LogicalExpressionHandler<LogicalExpression> handler = new DefaultLogicalExpressionHandler();
        String expression = "商品名称:手机";
        LogicalExpression tree = handler.convertToBinaryTree(expression);
        
        System.out.println("表达式: " + expression);
        System.out.println("树形结构:\n" + tree.prettyPrint());
        System.out.println();
    }

    /**
     * 测试比较表达式
     */
    private static void testComparisonExpression() {
        System.out.println("====== 测试比较表达式 ======");
        LogicalExpressionHandler<LogicalExpression> handler = new DefaultLogicalExpressionHandler();
        
        String[] expressions = {
            "价格 > 1000",
            "价格 >= 1000",
            "价格 < 1000",
            "价格 <= 1000"
        };
        
        for (String expression : expressions) {
            LogicalExpression tree = handler.convertToBinaryTree(expression);
            System.out.println("表达式: " + expression);
            System.out.println("树形结构:\n" + tree.prettyPrint());
            System.out.println();
        }
    }

    /**
     * 测试AND表达式
     */
    private static void testAndExpression() {
        System.out.println("====== 测试AND表达式 ======");
        LogicalExpressionHandler<LogicalExpression> handler = new DefaultLogicalExpressionHandler();
        String expression = "商品类别:电子产品 AND 价格 <= 5000";
        LogicalExpression tree = handler.convertToBinaryTree(expression);
        
        System.out.println("表达式: " + expression);
        System.out.println("树形结构:\n" + tree.prettyPrint());
        System.out.println();
    }

    /**
     * 测试OR表达式
     */
    private static void testOrExpression() {
        System.out.println("====== 测试OR表达式 ======");
        LogicalExpressionHandler<LogicalExpression> handler = new DefaultLogicalExpressionHandler();
        String expression = "商品名称:手机 OR 商品名称:平板";
        LogicalExpression tree = handler.convertToBinaryTree(expression);
        
        System.out.println("表达式: " + expression);
        System.out.println("树形结构:\n" + tree.prettyPrint());
        System.out.println();
    }

    /**
     * 测试嵌套表达式
     */
    private static void testNestedExpression() {
        System.out.println("====== 测试嵌套表达式 ======");
        LogicalExpressionHandler<LogicalExpression> handler = new DefaultLogicalExpressionHandler();
        String expression = "(商品名称:手机 OR 商品名称:平板) AND (价格 >= 1000 AND 价格 <= 5000)";
        LogicalExpression tree = handler.convertToBinaryTree(expression);
        
        System.out.println("表达式: " + expression);
        System.out.println("树形结构:\n" + tree.prettyPrint());
        System.out.println();
    }

    /**
     * 测试范围表达式
     */
    private static void testRangeExpression() {
        System.out.println("====== 测试范围表达式 ======");
        LogicalExpressionHandler<LogicalExpression> handler = new DefaultLogicalExpressionHandler();
        
        String[] expressions = {
            "创建日期 BETWEEN '2024-04-01' ~ '2024-05-01'",
            "价格:(1000~5000)"
        };
        
        for (String expression : expressions) {
            LogicalExpression tree = handler.convertToBinaryTree(expression);
            System.out.println("表达式: " + expression);
            System.out.println("树形结构:\n" + tree.prettyPrint());
            System.out.println();
        }
    }

    /**
     * 测试LIKE表达式
     */
    private static void testLikeExpression() {
        System.out.println("====== 测试LIKE表达式 ======");
        LogicalExpressionHandler<LogicalExpression> handler = new DefaultLogicalExpressionHandler();
        
        String[] expressions = {
            "商品名称 LIKE %手机%",
            "商品名称 LIKE *手机*",
            "~商品名称 LIKE %手机%"
        };
        
        for (String expression : expressions) {
            LogicalExpression tree = handler.convertToBinaryTree(expression);
            System.out.println("表达式: " + expression);
            System.out.println("树形结构:\n" + tree.prettyPrint());
            System.out.println();
        }
    }

    /**
     * 测试IN表达式
     */
    private static void testInExpression() {
        System.out.println("====== 测试IN表达式 ======");
        LogicalExpressionHandler<LogicalExpression> handler = new DefaultLogicalExpressionHandler();
        
        String[] expressions = {
            "商品类别 IN [电子产品,家具,厨具]",
            "商品类别:[电子产品,家具,厨具]"
        };
        
        for (String expression : expressions) {
            LogicalExpression tree = handler.convertToBinaryTree(expression);
            System.out.println("表达式: " + expression);
            System.out.println("树形结构:\n" + tree.prettyPrint());
            System.out.println();
        }
    }
} 