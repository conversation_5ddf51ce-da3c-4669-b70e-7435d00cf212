package com.chua.attach.hotspot.core.support.endpoint;

import com.chua.attach.hotspot.core.support.enums.ModuleType;
import com.chua.attach.hotspot.core.support.environment.Project;
import com.chua.attach.hotspot.core.support.environment.ReportEvent;
import com.chua.attach.hotspot.core.support.json.JSON;
import com.chua.attach.hotspot.core.support.log.LogFactory;
import com.chua.attach.hotspot.core.support.utils.NetAddress;
import com.chua.attach.hotspot.core.support.utils.StringUtils;
import io.zbus.mq.*;

import java.io.Closeable;
import java.io.IOException;
import java.net.InetAddress;
import java.util.List;

/**
 * <AUTHOR>
 */
@SuppressWarnings("ALL")
public class EndpointFactory extends Thread implements Closeable {

    final LogFactory logFactory = LogFactory.getInstance();

    static final EndpointFactory INSTANCE = new EndpointFactory();
    private Producer producer;
    private Broker broker = null;
    private String appName;
    private String topicName;

    public String applicationPort;
    public static String LOCAL = null;

    static {
        //根据网卡取本机配置的IP
        InetAddress inet = null;
        try {
            inet = InetAddress.getLocalHost();
        } catch (Exception e) {
           e.printStackTrace();
        }
        LOCAL = inet.getHostAddress();
    }

    public String appHost = "127.0.0.1";
    private String reportServerHost;
    private int reportServerPort;
    private String endpoinHost;

    private EndpointFactory() {
        Runtime.getRuntime().addShutdownHook(this);
    }

    public static EndpointFactory getInstance() {
        return INSTANCE;
    }

    /**
     * 初始化
     */
    public void init() {
        Project project = Project.getInstance();
        this.appName = project.getApplicationName();
        String reportServerAddress = project.getReportAddress();
        if(!StringUtils.isBlank(reportServerAddress)) {
            NetAddress netAddress = NetAddress.of(reportServerAddress);
            this.reportServerHost = netAddress.getHost();
            this.reportServerPort = netAddress.getPort() + 10000;
        }
        this.topicName = reportServerPort + "#report";
        this.applicationPort = String.valueOf(project.getApplicationPort());
        logFactory.info("当前服务器地址: {}({})", appName, applicationPort);
        if(reportServerHost != null && reportServerPort > 0) {
            logFactory.info("开始连接远程采集服务: {}:{}", reportServerHost, reportServerPort);
            connect();
        }
    }

    /**
     * 连接
     */
    public void connect() {
        if(StringUtils.isBlank(reportServerHost)) {
            return;
        }
        //创建Broker代表
        BrokerConfig brokerConfig = new BrokerConfig();
        String endpointUrl = reportServerHost + ":" + reportServerPort;
        brokerConfig.setTrackerList(endpointUrl);
        broker = new Broker(brokerConfig);
        ProducerConfig config = new ProducerConfig();
        config.setBroker(broker);
        this.producer = new Producer(config);
    }

    /**
     * 发送消息
     *
     * @param moduleType
     * @param message
     */
    public void publish(ModuleType moduleType , Object data) {
        if (null == producer || null == moduleType) {
            return;
        }

        String name = moduleType.name().toUpperCase();
        List<String> reportType = Project.getInstance().getReportType();
        if(!reportType.contains("ALL") && !reportType.contains(name)) {
            return;
        }
        ReportEvent<Object> reportEvent = new ReportEvent<>();
        reportEvent.setReportType(moduleType);
        reportEvent.setReportData(data);
        Message msg = new Message();
        msg.setTopic(topicName);
        msg.setBody(JSON.toJSONString(reportEvent));
        if(null == broker) {
            return;
        }
        try {
            producer.publish(msg);
        } catch (Throwable ignored) {
        }
    }

    @Override
    public void run() {
        try {
            close();
        } catch (IOException ignored) {
        }
    }

    public void restart() {
        if(null != broker) {
            logFactory.info("根据Spring重启远程采集服务");
        }
        try {
            this.close();
        } catch (IOException ignored) {
        }
        this.init();
    }

    @Override
    public void close() throws IOException {
        if(null != broker) {
            broker.close();
            broker = null;
        }
    }
}
