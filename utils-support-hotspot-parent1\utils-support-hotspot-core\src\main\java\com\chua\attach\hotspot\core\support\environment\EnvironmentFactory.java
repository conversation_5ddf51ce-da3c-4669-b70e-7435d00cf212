package com.chua.attach.hotspot.core.support.environment;

import com.chua.attach.hotspot.core.support.json.JSON;
import com.chua.attach.hotspot.core.support.json.JSONObject;
import com.chua.attach.hotspot.core.support.utils.StringUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * 环境工厂
 *
 * <AUTHOR>
 */
public class EnvironmentFactory {

    private JSONObject jsonObject;

    private static final EnvironmentFactory INSTANCE = new EnvironmentFactory();
    public void init(String args) {
        args = StringUtils.defaultValue(args, "");
        if(args.startsWith("{") ) {
            jsonObject = JSON.parseObject(args);
            check();
            return;
        }

        File file = new File(args);
        if(file.exists()) {
            try (FileInputStream fileInputStream = new FileInputStream(file)) {
                jsonObject = JSON.parseObject(fileInputStream, JSONObject.class);
            } catch (IOException e) {
               e.printStackTrace();
            }
        }
        check();
    }

    private void check() {
        if(null == jsonObject) {
            synchronized (this) {
                if(null == jsonObject) {
                    jsonObject = new JSONObject();
                }
            }
        }
    }

    private EnvironmentFactory() {
    }

    /**
     * 获取实例
     *
     * @return {@link EnvironmentFactory}
     */
    public static EnvironmentFactory getInstance() {
        return INSTANCE;
    }

    /**
     * ge字符串
     *
     * @param name         名称
     * @param defaultValue 违约值
     * @return {@link String}
     */
    public String getString(String name, String defaultValue) {
        return StringUtils.defaultValue(jsonObject.getString(name), defaultValue);
    }

    /**
     * get类型
     *
     * @param type 类型
     * @return {@link T}
     */
    public <T>T getType(Class<T> type) {
        return jsonObject.toJavaObject(type);
    }

    public void set(String name, String resolvePlaceholders) {
        jsonObject.put(name, resolvePlaceholders);
    }

    /**
     * 判断配置是否相等
     *
     * @param indicator 键
     * @param aTrue     真
     * @return boolean
     */
    public boolean equalsConfig(String indicator, String aTrue) {
        return getString(indicator, "true").equals(aTrue);
    }
}
