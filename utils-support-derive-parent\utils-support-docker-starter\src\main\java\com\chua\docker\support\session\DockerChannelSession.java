package com.chua.docker.support.session;

import com.chua.common.support.protocol.ClientSetting;
import com.chua.common.support.protocol.channel.Channel;
import com.chua.common.support.protocol.session.AbstractChannelSession;
import com.github.dockerjava.api.DockerClient;
import com.github.dockerjava.api.async.ResultCallback;
import com.github.dockerjava.api.command.ExecCreateCmdResponse;
import com.github.dockerjava.api.model.Frame;
import com.github.dockerjava.api.model.Statistics;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * Docker 通道会话
 * 
 * 提供 Docker 容器的交互式会话功能，支持命令执行、日志获取、统计信息监控等。
 * 
 * <AUTHOR>
 * @since 2024/12/25
 */
@Slf4j
public class DockerChannelSession extends AbstractChannelSession {

    private final DockerClient dockerClient;
    private final ClientSetting clientSetting;

    public DockerChannelSession(DockerClient dockerClient, ClientSetting clientSetting) {
        this.dockerClient = dockerClient;
        this.clientSetting = clientSetting;
    }

    @Override
    protected Channel openChannel(String type) {
        // Docker 不需要传统的通道概念，返回 null
        return null;
    }

    /**
     * 在容器中执行命令
     * 
     * @param containerId 容器ID
     * @param command 要执行的命令
     * @param timeout 超时时间（秒）
     * @return 命令执行结果
     */
    public String executeCommand(String containerId, String[] command, int timeout) {
        try {
            // 创建执行命令
            ExecCreateCmdResponse execCreateCmdResponse = dockerClient.execCreateCmd(containerId)
                    .withAttachStdout(true)
                    .withAttachStderr(true)
                    .withCmd(command)
                    .exec();

            // 执行命令并获取输出
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ByteArrayOutputStream errorStream = new ByteArrayOutputStream();
            CountDownLatch latch = new CountDownLatch(1);

            dockerClient.execStartCmd(execCreateCmdResponse.getId())
                    .exec(new ResultCallback<Frame>() {
                        @Override
                        public void onStart(Closeable closeable) {
                            log.debug("开始执行命令: {}", String.join(" ", command));
                        }

                        @Override
                        public void onNext(Frame frame) {
                            try {
                                byte[] payload = frame.getPayload();
                                if (frame.getStreamType() == Frame.StreamType.STDOUT) {
                                    outputStream.write(payload);
                                } else if (frame.getStreamType() == Frame.StreamType.STDERR) {
                                    errorStream.write(payload);
                                }
                            } catch (IOException e) {
                                log.error("处理命令输出失败", e);
                            }
                        }

                        @Override
                        public void onError(Throwable throwable) {
                            log.error("命令执行出错", throwable);
                            latch.countDown();
                        }

                        @Override
                        public void onComplete() {
                            log.debug("命令执行完成");
                            latch.countDown();
                        }

                        @Override
                        public void close() throws IOException {
                            // 清理资源
                        }
                    });

            // 等待命令执行完成
            boolean completed = latch.await(timeout, TimeUnit.SECONDS);
            if (!completed) {
                log.warn("命令执行超时: {}", String.join(" ", command));
                return "命令执行超时";
            }

            // 返回执行结果
            String output = outputStream.toString(StandardCharsets.UTF_8);
            String error = errorStream.toString(StandardCharsets.UTF_8);
            
            if (!error.isEmpty()) {
                log.warn("命令执行有错误输出: {}", error);
                return output + "\nERROR: " + error;
            }
            
            return output;

        } catch (Exception e) {
            log.error("执行命令失败: {}", String.join(" ", command), e);
            return "命令执行失败: " + e.getMessage();
        }
    }

    /**
     * 获取容器日志
     * 
     * @param containerId 容器ID
     * @param tail 获取最后多少行日志
     * @param follow 是否跟踪日志
     * @return 日志内容
     */
    public String getContainerLogs(String containerId, int tail, boolean follow) {
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            CountDownLatch latch = new CountDownLatch(1);

            dockerClient.logContainerCmd(containerId)
                    .withStdOut(true)
                    .withStdErr(true)
                    .withTail(tail)
                    .withFollowStream(follow)
                    .exec(new ResultCallback<Frame>() {
                        @Override
                        public void onStart(Closeable closeable) {
                            log.debug("开始获取容器日志: {}", containerId);
                        }

                        @Override
                        public void onNext(Frame frame) {
                            try {
                                outputStream.write(frame.getPayload());
                            } catch (IOException e) {
                                log.error("处理日志输出失败", e);
                            }
                        }

                        @Override
                        public void onError(Throwable throwable) {
                            log.error("获取日志出错", throwable);
                            latch.countDown();
                        }

                        @Override
                        public void onComplete() {
                            log.debug("日志获取完成");
                            latch.countDown();
                        }

                        @Override
                        public void close() throws IOException {
                            // 清理资源
                        }
                    });

            // 如果不是跟踪模式，等待日志获取完成
            if (!follow) {
                latch.await(30, TimeUnit.SECONDS);
            }

            return outputStream.toString(StandardCharsets.UTF_8);

        } catch (Exception e) {
            log.error("获取容器日志失败: {}", containerId, e);
            return "获取日志失败: " + e.getMessage();
        }
    }

    /**
     * 获取容器统计信息
     * 
     * @param containerId 容器ID
     * @return 统计信息
     */
    public Statistics getContainerStats(String containerId) {
        try {
            CountDownLatch latch = new CountDownLatch(1);
            Statistics[] stats = new Statistics[1];

            dockerClient.statsCmd(containerId)
                    .withNoStream(true)
                    .exec(new ResultCallback<Statistics>() {
                        @Override
                        public void onStart(Closeable closeable) {
                            log.debug("开始获取容器统计信息: {}", containerId);
                        }

                        @Override
                        public void onNext(Statistics statistics) {
                            stats[0] = statistics;
                            latch.countDown();
                        }

                        @Override
                        public void onError(Throwable throwable) {
                            log.error("获取统计信息出错", throwable);
                            latch.countDown();
                        }

                        @Override
                        public void onComplete() {
                            log.debug("统计信息获取完成");
                            latch.countDown();
                        }

                        @Override
                        public void close() throws IOException {
                            // 清理资源
                        }
                    });

            // 等待统计信息获取完成
            latch.await(10, TimeUnit.SECONDS);
            return stats[0];

        } catch (Exception e) {
            log.error("获取容器统计信息失败: {}", containerId, e);
            return null;
        }
    }

    @Override
    public void close() throws Exception {
        log.info("Docker 通道会话已关闭");
    }

    @Override
    public boolean isClosed() {
        return false;
    }
}
