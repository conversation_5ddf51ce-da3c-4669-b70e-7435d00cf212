package com.chua.common.support.protocol.client;

import com.chua.common.support.protocol.ClientSetting;
import com.chua.common.support.protocol.session.Session;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Client接口定义了客户端的连接行为。
 *
 * 该接口的实现负责建立和管理与服务端的连接。通过实现此接口，
 * 类可以提供特定于服务端连接的逻辑，包括连接的建立、维护和断开。
 *
 * <AUTHOR>
 * @since 2024/6/28
 */
public abstract class AbstractClient implements Client {

    protected ClientSetting clientSetting;

    protected Map<String, Session> sessions = new ConcurrentHashMap<>();
    public Session session;

    public AbstractClient(ClientSetting clientSetting) {
        this.clientSetting = clientSetting;
    }

    @Override
    public void connect() {
        this.session = createSession("default");
    }

    @Override
    public Session createSession(String name) {
        Session session = sessions.computeIfAbsent(name, key -> createSession());
        if(session.isClosed()) {
            try (Session session1 = sessions.remove(name)) {
            } catch (Exception ignored) {
            }
            session = sessions.computeIfAbsent(name, key -> createSession());
        }
        return session;
    }

    @Override
    public void closeSession(String exec) {
        try (Session session = sessions.remove(exec)) {
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void close() throws Exception {
        for (Map.Entry<String, Session> entry : sessions.entrySet()) {
            try {
                entry.getValue().close();
            } catch (Exception ignored) {
            }
        }
    }
    @Override
    public Session getSession() {
        return session;
    }
    /**
     * 创建一个新的会话对象。
     *
     * 该方法是一个抽象方法，需要在子类中具体实现。它的目的是为了在不同的情况下
     * 或者针对不同的需求，创建具有特定行为或属性的会话对象。通过这个方法，调用者
     * 可以获得一个会话实例，而不需要关心具体的实现细节。
     *
     * @return 返回一个新的会话对象。这个对象的具体类型取决于实现该方法的子类。
     */
    protected abstract Session createSession();


    @Override
    public boolean isConnected() {
        return session != null && !session.isClosed();
    }
}

