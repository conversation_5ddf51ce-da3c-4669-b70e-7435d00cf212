<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- 父项目信息 -->
    <parent>
        <groupId>com.chua</groupId>
        <artifactId>utils-support-derive-parent</artifactId>
        <version>********</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <!-- 模块基本信息 -->
    <artifactId>utils-support-sshj-starter</artifactId>
    <name>Utils Support SSHJ Starter</name>
    <description>
        SSHJ SSH 客户端模块，提供现代化的 SSH 连接和文件传输功能。

        主要功能：
        - 🔐 SSH 连接：安全的 SSH 协议连接和认证
        - 📁 SFTP 传输：高效的 SFTP 文件上传下载
        - 💻 命令执行：远程命令执行和结果获取
        - 🔑 多种认证：密码、公钥、键盘交互认证
        - 🌐 端口转发：本地和远程端口转发功能
        - 📊 会话管理：SSH 会话生命周期管理
        - 🔧 连接池：SSH 连接池和复用机制
        - 🛡️ 安全配置：主机密钥验证和安全策略

        适用场景：
        - 远程服务器管理
        - 自动化部署脚本
        - 文件同步传输
        - 远程监控系统
        - DevOps 工具集成
        - 云服务器操作
    </description>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <lombok.version>1.18.26</lombok.version>
        <guava.version>32.1.2-jre</guava.version>
    </properties>

    <dependencies>
        <!-- SSHJ -->
        <dependency>
            <groupId>com.hierynomus</groupId>
            <artifactId>sshj</artifactId>
            <version>0.40.0</version>
        </dependency>
        
        <!-- Common Starter -->
        <dependency>
            <groupId>com.chua</groupId>
            <artifactId>utils-support-common-starter</artifactId>
        </dependency>
    </dependencies>

</project>