package com.chua.attach.hotspot.support.agent;

import com.chua.attach.hotspot.core.support.inst.InstrumentationFactory;
import com.chua.attach.hotspot.core.support.log.LogFactory;
import com.chua.attach.hotspot.core.support.plugin.Plugin;
import com.chua.attach.hotspot.core.support.plugin.PluginFactory;
import com.chua.attach.hotspot.core.support.server.ServerFactory;
import com.chua.attach.hotspot.core.support.span.Span;
import com.chua.attach.hotspot.support.transform.TransformFactory;
import com.chua.attach.hotspot.support.transform.VersionTransform;
import net.bytebuddy.agent.builder.AgentBuilder;
import net.bytebuddy.description.type.TypeDescription;
import net.bytebuddy.matcher.ElementMatcher;
import net.bytebuddy.matcher.ElementMatchers;

/**
 * <AUTHOR>
 */
public class AgentFactory {

    static AgentFactory INSTANCE = new AgentFactory();
    final PluginFactory pluginFactory = PluginFactory.getInstance();
    final LogFactory logFactory = LogFactory.getInstance();
    AgentFactory() {

    }


    public static AgentFactory getInstance() {
        return INSTANCE;
    }


    public void init() {
        AgentBuilder.Default agentBuilder = new AgentBuilder.Default();
        AgentBuilder builder = agentBuilder
                .ignore(ElementMatchers.isSubTypeOf(Span.class))
                .ignore(ElementMatchers.isSubTypeOf(com.chua.attach.hotspot.core.support.transform.Span.class))
                .ignore(ElementMatchers.nameStartsWith("com.chua.attach.hotspot"))
                .ignore(ElementMatchers.nameStartsWith("io.micrometer.core"))
                .ignore(ElementMatchers.nameContainsIgnoreCase("GeneratedMethodAccessor"))
                .with(AgentBuilder.RedefinitionStrategy.RETRANSFORMATION)
                .with(AgentBuilder.TypeStrategy.Default.REBASE)
                .with(new AgentBuilder.InitializationStrategy.SelfInjection.Eager());
        AgentBuilder.Identified.Extendable transform = null;
        for (Plugin plugin : pluginFactory.toList()) {
            String name = plugin.name();
            if (null == name) {
                continue;
            }
            if (pluginFactory.isPass(name)) {
                plugin.init();
                ElementMatcher<? super TypeDescription> type = plugin.type();
                if (null != type) {
                    VersionTransform versionTransform = TransformFactory.getInstance().init(plugin);
                    if(null == versionTransform) {
                        return;
                    }
                    if (null == transform) {
                        transform =  builder.type(type).transform(versionTransform);
                    }

                    transform = transform.type(type).transform(versionTransform);
                }
                transform = plugin.transforms(transform);
                plugin.initComplete();
            }
        }
        if (null == transform) {
            return;
        }

        ServerFactory.getInstance().init(transform);

        transform.with(new AgentListener())
                .installOn(InstrumentationFactory.getInstance().get());
        PluginFactory.getInstance().finish();
    }



}
