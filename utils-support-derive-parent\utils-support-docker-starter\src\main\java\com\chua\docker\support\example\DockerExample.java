package com.chua.docker.support.example;

import com.chua.common.support.protocol.ClientSetting;
import com.chua.docker.support.client.DockerClient;
import com.chua.docker.support.session.DockerChannelSession;
import com.github.dockerjava.api.model.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Docker 使用示例
 * 
 * 演示如何使用 DockerClient 进行容器和镜像管理
 * 
 * <AUTHOR>
 * @since 2024/12/25
 */
@Slf4j
public class DockerExample {

    /**
     * 基本 Docker 操作示例
     */
    public static void basicDockerExample() {
        log.info("=== 基本 Docker 操作示例 ===");
        
        // 创建 Docker 客户端
        ClientSetting clientSetting = ClientSetting.builder()
                .url("tcp://localhost:2376")  // Docker 守护进程地址
                .build();
        
        try (DockerClient dockerClient = new DockerClient(clientSetting)) {
            // 测试连接
            boolean connected = dockerClient.testConnection();
            log.info("Docker 连接状态: {}", connected ? "成功" : "失败");
            
            if (!connected) {
                log.error("无法连接到 Docker，请检查 Docker 是否运行");
                return;
            }
            
            // 获取 Docker 信息
            Info dockerInfo = dockerClient.getDockerInfo();
            log.info("Docker 版本: {}", dockerInfo.getServerVersion());
            log.info("容器数量: {}", dockerInfo.getContainers());
            log.info("镜像数量: {}", dockerInfo.getImages());
            
            // 列出所有容器
            List<Container> containers = dockerClient.listContainers(true);
            log.info("当前容器数量: {}", containers.size());
            containers.forEach(container -> {
                log.info("容器: {} - {} - {}", 
                    container.getId().substring(0, 12),
                    container.getImage(),
                    container.getState()
                );
            });
            
            // 列出所有镜像
            List<Image> images = dockerClient.listImages();
            log.info("当前镜像数量: {}", images.size());
            images.forEach(image -> {
                String[] repoTags = image.getRepoTags();
                if (repoTags != null && repoTags.length > 0) {
                    log.info("镜像: {} - 大小: {} MB", 
                        repoTags[0], 
                        image.getSize() / (1024 * 1024)
                    );
                }
            });
            
        } catch (Exception e) {
            log.error("Docker 操作失败", e);
        }
    }

    /**
     * 容器管理示例
     */
    public static void containerManagementExample() {
        log.info("=== 容器管理示例 ===");
        
        ClientSetting clientSetting = ClientSetting.builder()
                .url("tcp://localhost:2376")
                .build();
        
        try (DockerClient dockerClient = new DockerClient(clientSetting)) {
            String imageName = "nginx:latest";
            String containerName = "test-nginx";
            
            // 拉取镜像
            log.info("拉取镜像: {}", imageName);
            dockerClient.pullImage("nginx", "latest");
            
            // 创建容器（带端口映射）
            Map<String, String> portBindings = new HashMap<>();
            portBindings.put("80", "8080");  // 容器80端口映射到主机8080端口
            
            List<String> env = Arrays.asList("NGINX_HOST=localhost");
            
            CreateContainerResponse container = dockerClient.createContainer(
                imageName, containerName, portBindings, env, null
            );
            String containerId = container.getId();
            log.info("容器创建成功: {}", containerId);
            
            // 启动容器
            dockerClient.startContainer(containerId);
            log.info("容器启动成功");
            
            // 获取容器详细信息
            InspectContainerResponse containerInfo = dockerClient.inspectContainer(containerId);
            log.info("容器状态: {}", containerInfo.getState().getStatus());
            log.info("容器IP: {}", containerInfo.getNetworkSettings().getIpAddress());
            
            // 在容器中执行命令
            DockerChannelSession session = dockerClient.createSession();
            String result = session.executeCommand(containerId, 
                new String[]{"nginx", "-v"}, 10);
            log.info("命令执行结果: {}", result);
            
            // 获取容器日志
            String logs = session.getContainerLogs(containerId, 50, false);
            log.info("容器日志: {}", logs);
            
            // 获取容器统计信息
            Statistics stats = session.getContainerStats(containerId);
            if (stats != null) {
                log.info("内存使用: {} MB", 
                    stats.getMemoryStats().getUsage() / (1024 * 1024));
            }
            
            // 停止容器
            dockerClient.stopContainer(containerId, 10);
            log.info("容器已停止");
            
            // 删除容器
            dockerClient.removeContainer(containerId, true, true);
            log.info("容器已删除");
            
        } catch (Exception e) {
            log.error("容器管理操作失败", e);
        }
    }

    /**
     * 镜像管理示例
     */
    public static void imageManagementExample() {
        log.info("=== 镜像管理示例 ===");

        ClientSetting clientSetting = ClientSetting.builder()
                .url("tcp://localhost:2376")
                .build();

        try (DockerClient dockerClient = new DockerClient(clientSetting)) {
            String imageName = "alpine";
            String tag = "latest";

            // 拉取镜像
            log.info("拉取镜像: {}:{}", imageName, tag);
            dockerClient.pullImage(imageName, tag);

            // 获取镜像信息
            List<Image> images = dockerClient.listImages();
            Image alpineImage = images.stream()
                    .filter(img -> img.getRepoTags() != null &&
                           Arrays.asList(img.getRepoTags()).contains(imageName + ":" + tag))
                    .findFirst()
                    .orElse(null);

            if (alpineImage != null) {
                String imageId = alpineImage.getId();
                log.info("找到镜像: {}", imageId);

                // 获取镜像详细信息
                InspectImageResponse imageInfo = dockerClient.inspectImage(imageId);
                log.info("镜像架构: {}", imageInfo.getArch());
                log.info("镜像大小: {} MB", imageInfo.getSize() / (1024 * 1024));

                // 标记镜像
                dockerClient.tagImage(imageId, "my-alpine", "v1.0");
                log.info("镜像标记成功");

                // 删除标记的镜像
                dockerClient.removeImage("my-alpine:v1.0", false);
                log.info("标记的镜像已删除");
            }

        } catch (Exception e) {
            log.error("镜像管理操作失败", e);
        }
    }

    /**
     * 镜像搜索和版本管理示例
     */
    public static void imageSearchAndVersionExample() {
        log.info("=== 镜像搜索和版本管理示例 ===");

        ClientSetting clientSetting = ClientSetting.builder()
                .url("tcp://localhost:2376")
                .build();

        try (DockerClient dockerClient = new DockerClient(clientSetting)) {
            // 模糊搜索镜像
            String searchTerm = "nginx";
            log.info("搜索包含 '{}' 的镜像:", searchTerm);
            List<Image> searchResults = dockerClient.searchImagesByName(searchTerm);
            searchResults.forEach(image -> {
                if (image.getRepoTags() != null) {
                    Arrays.stream(image.getRepoTags()).forEach(tag -> {
                        log.info("找到镜像: {} (ID: {})", tag,
                                image.getId().substring(0, 12));
                    });
                }
            });

            // 获取指定镜像的所有版本
            String repository = "nginx";
            log.info("获取 '{}' 镜像的所有版本:", repository);
            List<String> versions = dockerClient.getImageVersions(repository);
            versions.forEach(version -> log.info("版本: {}", version));

            // 获取镜像版本详细信息
            log.info("获取 '{}' 镜像的详细版本信息:", repository);
            List<DockerClient.ImageVersionInfo> versionDetails =
                    dockerClient.getImageVersionDetails(repository);
            versionDetails.forEach(info -> {
                log.info("详细信息: {}", info.toString());
                log.info("  - 镜像ID: {}", info.getImageId());
                log.info("  - 仓库: {}", info.getRepository());
                log.info("  - 版本: {}", info.getVersion());
                log.info("  - 完整标签: {}", info.getFullTag());
                log.info("  - 大小: {}", info.getSizeFormatted());
                log.info("  - 创建时间: {}", new java.util.Date(info.getCreated() * 1000));
            });

            // 演示多个搜索词
            String[] searchTerms = {"alpine", "ubuntu", "redis"};
            for (String term : searchTerms) {
                log.info("搜索 '{}' 相关镜像:", term);
                List<Image> results = dockerClient.searchImagesByName(term);
                log.info("找到 {} 个相关镜像", results.size());

                // 显示前3个结果
                results.stream().limit(3).forEach(image -> {
                    if (image.getRepoTags() != null && image.getRepoTags().length > 0) {
                        log.info("  - {}", image.getRepoTags()[0]);
                    }
                });
            }

        } catch (Exception e) {
            log.error("镜像搜索和版本管理操作失败", e);
        }
    }

    /**
     * 网络和卷管理示例
     */
    public static void networkAndVolumeExample() {
        log.info("=== 网络和卷管理示例 ===");
        
        ClientSetting clientSetting = ClientSetting.builder()
                .url("tcp://localhost:2376")
                .build();
        
        try (DockerClient dockerClient = new DockerClient(clientSetting)) {
            // 网络管理
            String networkName = "test-network";
            CreateNetworkResponse network = dockerClient.createNetwork(networkName, "bridge");
            log.info("网络创建成功: {}", network.getId());
            
            List<Network> networks = dockerClient.listNetworks();
            log.info("当前网络数量: {}", networks.size());
            
            dockerClient.removeNetwork(network.getId());
            log.info("网络已删除");
            
            // 卷管理
            String volumeName = "test-volume";
            CreateVolumeResponse volume = dockerClient.createVolume(volumeName, "local");
            log.info("卷创建成功: {}", volume.getName());
            
            ListVolumesResponse volumes = dockerClient.listVolumes();
            log.info("当前卷数量: {}", volumes.getVolumes().size());
            
            dockerClient.removeVolume(volumeName, false);
            log.info("卷已删除");
            
        } catch (Exception e) {
            log.error("网络和卷管理操作失败", e);
        }
    }

    public static void main(String[] args) {
        basicDockerExample();
        containerManagementExample();
        imageManagementExample();
        imageSearchAndVersionExample();
        networkAndVolumeExample();
    }
}
