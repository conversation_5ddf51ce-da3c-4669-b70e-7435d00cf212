<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- 父项目信息 -->
    <parent>
        <groupId>com.chua</groupId>
        <artifactId>utils-support-derive-parent</artifactId>
        <version>********</version>
    </parent>

    <!-- 模块基本信息 -->
    <artifactId>utils-support-tio-starter</artifactId>
    <name>Utils Support TIO Starter</name>
    <description>
        TIO 高性能网络通信模块，提供基于 NIO 的网络编程框架。

        主要功能：
        - 🚀 高性能 NIO：基于 Java NIO 的高性能网络通信
        - 🌐 HTTP 服务器：轻量级 HTTP 服务器实现
        - 🔌 WebSocket：完整的 WebSocket 协议支持
        - 📡 TCP/UDP：TCP 和 UDP 协议的封装和优化
        - 🎯 连接管理：连接池、心跳检测、断线重连
        - 📊 集群支持：分布式集群和负载均衡
        - 🔧 协议定制：自定义协议解析和编码
        - 💾 消息队列：内置消息队列和异步处理

        适用场景：
        - 高并发 Web 服务
        - 实时通信系统
        - 游戏服务器开发
        - IoT 设备通信
        - 微服务通信
        - 推送服务系统
    </description>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.litongjava</groupId>
            <artifactId>tio-http-server</artifactId>
            <version>3.7.3.v20250301-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.chua</groupId>
            <artifactId>utils-support-common-starter</artifactId>
        </dependency>
    </dependencies>

</project>