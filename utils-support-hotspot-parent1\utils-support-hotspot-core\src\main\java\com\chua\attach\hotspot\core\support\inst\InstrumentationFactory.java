package com.chua.attach.hotspot.core.support.inst;

import com.chua.attach.hotspot.core.support.transform.TransformerImpl;

import java.lang.instrument.ClassDefinition;
import java.lang.instrument.Instrumentation;
import java.lang.instrument.UnmodifiableClassException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class InstrumentationFactory {

    static final InstrumentationFactory INSTANCE = new InstrumentationFactory();
    public Instrumentation instrumentation;
    private final Map<String, Class<?>> cache = new ConcurrentHashMap<>();

    private InstrumentationFactory() {
    }

    /**
     * 刷新
     */
    public void refresh() {
        Class[] allLoadedClasses = instrumentation.getAllLoadedClasses();
        for (Class allLoadedClass : allLoadedClasses) {
            cache.put(allLoadedClass.getTypeName(), allLoadedClass);
        }
    }


    /**
     * get类型
     *
     * @param name 名称
     * @return {@link Class}<{@link ?}>
     */
    public Class<?> getType(String name) {
        if(cache.containsKey(name)) {
            return cache.get(name);
        }

        refresh();
        for (Map.Entry<String, Class<?>> entry : cache.entrySet()) {
            String entryKey = entry.getKey();
            if(name.equals(entryKey)  || entryKey.endsWith(name)) {
                return entry.getValue();
            }
        }

        return null;
    }
    /**
     * 获取实例
     *
     * @return {@link InstrumentationFactory}
     */
    public static InstrumentationFactory getInstance() {
        return INSTANCE;
    }

    public void init(Instrumentation instrumentation) {
        this.instrumentation = instrumentation;
    }

    /**
     * get
     *
     * @return {@link Instrumentation}
     */
    public Instrumentation get() {
        return instrumentation;
    }

    /**
     * 重定义
     *
     * @param type  类型
     * @param bytes 字节
     * @throws Exception 异常
     */
    public void rebase(Class<?> type, byte[] bytes) throws Exception {
        if(null == type) {
            return;
        }
        ClassDefinition classDefinition = new ClassDefinition(type, bytes);
        instrumentation.redefineClasses(classDefinition);
    }

    public void retransformClasses(Class[] array) throws UnmodifiableClassException {
        instrumentation.retransformClasses(array);
    }

    public void addTransformer(TransformerImpl transformer, boolean b) {
        instrumentation.addTransformer(transformer, b);
    }
}
