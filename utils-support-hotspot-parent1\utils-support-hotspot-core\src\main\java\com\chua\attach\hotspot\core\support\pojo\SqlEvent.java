package com.chua.attach.hotspot.core.support.pojo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * sql事件
 * <AUTHOR>
 * @since 2024/9/7
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SqlEvent extends TimestampEvent implements Serializable {

    /**
     * sql
     */
    private String sql;

    /**
     * 线程
     */
    private String thread;

    /**
     * 事件
     */
    private String event;
    /**
     * 类名
     */
    private String className;


}
