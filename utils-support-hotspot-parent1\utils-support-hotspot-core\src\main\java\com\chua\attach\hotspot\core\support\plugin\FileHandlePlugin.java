package com.chua.attach.hotspot.core.support.plugin;

import com.chua.attach.hotspot.core.support.inst.InstrumentationFactory;
import com.chua.attach.hotspot.core.support.transform.Spec;
import com.chua.attach.hotspot.core.support.transform.TransformerImpl;
import net.bytebuddy.description.type.TypeDescription;
import net.bytebuddy.dynamic.DynamicType;
import net.bytebuddy.matcher.ElementMatcher;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.PrintStream;
import java.io.RandomAccessFile;
import java.nio.channels.FileChannel;
import java.nio.channels.spi.AbstractInterruptibleChannel;
import java.nio.channels.spi.AbstractSelectableChannel;
import java.nio.channels.spi.AbstractSelector;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.zip.ZipFile;

/**
 * <AUTHOR>
 * @since 2024/11/7
 */
public class FileHandlePlugin implements Plugin{
    @Override
    public String name() {
        return "filehandle";
    }

    @Override
    public void init() {

    }

    @Override
    public DynamicType.Builder.MethodDefinition.ReceiverTypeDefinition<?> transform(DynamicType.Builder<?> builder) {
        return null;
    }

    @Override
    public ElementMatcher<? super TypeDescription> type() {
        return null;
    }

    @Override
    public void initComplete() {
        InstrumentationFactory.getInstance().addTransformer(new TransformerImpl(Spec.createSpec()), true);
        List<Class> rt = new ArrayList<>(Arrays.asList(
                FileInputStream.class,
                FileOutputStream.class,
                RandomAccessFile.class,
                Exception.class,
                ZipFile.class,
                AbstractSelectableChannel.class,
                AbstractInterruptibleChannel.class,
                FileChannel.class,
                PrintStream.class,
                AbstractSelector.class
        ));
        try {
            Class<?> aClass = Class.forName("java.net.PlainSocketImpl");
            rt.add(aClass);
        } catch (ClassNotFoundException ignored) {
        }
        try {
            InstrumentationFactory.getInstance().retransformClasses(rt.toArray(new Class[0]));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
