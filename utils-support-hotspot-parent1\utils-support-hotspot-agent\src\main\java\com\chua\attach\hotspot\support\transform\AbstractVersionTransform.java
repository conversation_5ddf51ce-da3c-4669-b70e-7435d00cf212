package com.chua.attach.hotspot.support.transform;

import com.chua.attach.hotspot.core.support.plugin.Plugin;
import net.bytebuddy.dynamic.DynamicType;

/**
 * Transform
 *
 * <AUTHOR>
 */
public abstract class AbstractVersionTransform implements VersionTransform {

    protected Plugin plugin;

    public void setPlugin(Plugin plugin) {
        this.plugin = plugin;
    }


    public DynamicType.Builder<?> transformBuilder(DynamicType.Builder<?> builder) {
        return null == plugin ? null : plugin.transform(builder);
    }
}
