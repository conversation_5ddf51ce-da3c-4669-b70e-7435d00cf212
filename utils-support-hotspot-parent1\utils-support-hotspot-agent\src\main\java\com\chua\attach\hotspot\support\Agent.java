package com.chua.attach.hotspot.support;

import com.chua.attach.hotspot.core.support.Constant;
import com.chua.attach.hotspot.core.support.endpoint.EndpointFactory;
import com.chua.attach.hotspot.core.support.environment.EnvironmentFactory;
import com.chua.attach.hotspot.core.support.hotspot.HotspotFactory;
import com.chua.attach.hotspot.core.support.inst.InstrumentationFactory;
import com.chua.attach.hotspot.core.support.log.LogFactory;
import com.chua.attach.hotspot.core.support.plugin.PluginFactory;
import com.chua.attach.hotspot.support.agent.AgentFactory;

import java.lang.instrument.Instrumentation;

/**
 * <AUTHOR>
 */
public class Agent {

    public static void premain(String args, Instrumentation instrumentation) {
        InstrumentationFactory.getInstance().init(instrumentation);
        EnvironmentFactory.getInstance().init(args);
        LogFactory.getInstance().init();
        PluginFactory.getInstance().init();
        EndpointFactory.getInstance().init();
        AgentFactory.getInstance().init();
        HotspotFactory.getInstance().init();
        Constant.init();
    }
}
