<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <!-- 继承父项目 -->
    <parent>
        <groupId>com.chua</groupId>
        <artifactId>utils-support-derive-parent</artifactId>
        <version>********</version>
    </parent>

    <!-- 模块基本信息 -->
    <artifactId>utils-support-docker-starter</artifactId>
    <name>Utils Support Docker Starter</name>
    <description>
        Docker 容器化支持模块，提供 Docker 客户端功能。
        
        主要功能：
        - 🐳 Docker 容器管理：创建、启动、停止、删除容器
        - 📦 镜像管理：拉取、构建、推送、删除镜像
        - 🔍 容器监控：获取容器状态、日志、统计信息
        - 📊 系统信息：获取 Docker 系统信息和版本
        - 🌐 网络管理：创建、删除、管理 Docker 网络
        - 💾 卷管理：创建、删除、管理 Docker 卷
        - 🔧 执行命令：在容器中执行命令
        - 📋 事件监听：监听 Docker 事件
        
        基于 Docker Java API 实现，提供统一的客户端接口。
    </description>

    <!-- 项目属性 -->
    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <docker.java.version>3.4.0</docker.java.version>
    </properties>

    <!-- 依赖管理 -->
    <dependencies>
        <!-- 核心依赖 -->
        <dependency>
            <groupId>com.chua</groupId>
            <artifactId>utils-support-common-starter</artifactId>
            <version>********</version>
        </dependency>

        <!-- Docker Java API -->
        <dependency>
            <groupId>com.github.docker-java</groupId>
            <artifactId>docker-java-core</artifactId>
            <version>${docker.java.version}</version>
        </dependency>
        
        <dependency>
            <groupId>com.github.docker-java</groupId>
            <artifactId>docker-java-transport-httpclient5</artifactId>
            <version>${docker.java.version}</version>
        </dependency>

        <!-- JSON 处理 -->
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>2.0.46</version>
        </dependency>

        <!-- 日志框架 -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>2.0.12</version>
        </dependency>

        <!-- 工具库 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.22</version>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.10.1</version>
            <scope>test</scope>
        </dependency>
        
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.5.4</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <!-- 构建配置 -->
    <build>
        <plugins>
            <!-- Maven 编译插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.13.0</version>
                <configuration>
                    <source>21</source>
                    <target>21</target>
                    <release>21</release>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.34</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            
            <!-- Maven Surefire 测试插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.2.5</version>
                <configuration>
                    <skipTests>false</skipTests>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
