package com.chua.common.support.lang.engine.expression.resolver;

import com.chua.common.support.lang.engine.expression.node.LogicalExpression;

/**
 * 逻辑表达式处理器空格处理测试
 * 测试各种符号前后存在空格的情况
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/04/09
 */
public class DefaultLogicalExpressionHandlerSpaceTest {

    public static void main(String[] args) {
        testColonWithSpaces();
        testBracketsWithSpaces();
        testOperatorsWithSpaces();
        testComplexSpaces();
    }

    /**
     * 测试冒号周围有空格的情况
     */
    private static void testColonWithSpaces() {
        System.out.println("====== 测试冒号周围有空格的情况 ======");
        LogicalExpressionHandler<LogicalExpression> handler = new DefaultLogicalExpressionHandler();
        
        String[] expressions = {
            "商品名称:手机",            // 标准格式
            "商品名称 :手机",           // 冒号前有空格
            "商品名称: 手机",           // 冒号后有空格
            "商品名称 : 手机"           // 冒号前后都有空格
        };
        
        for (String expression : expressions) {
            LogicalExpression tree = handler.convertToBinaryTree(expression);
            System.out.println("表达式: " + expression);
            System.out.println("树形结构:\n" + tree.prettyPrint());
            System.out.println();
        }
    }

    /**
     * 测试括号周围有空格的情况
     */
    private static void testBracketsWithSpaces() {
        System.out.println("====== 测试括号周围有空格的情况 ======");
        LogicalExpressionHandler<LogicalExpression> handler = new DefaultLogicalExpressionHandler();
        
        String[] expressions = {
            "价格:(1000~5000)",         // 标准格式
            "价格 :(1000~5000)",        // 冒号前有空格
            "价格: (1000~5000)",        // 冒号和括号之间有空格
            "价格 : (1000~5000)",       // 冒号前后都有空格
            "价格:( 1000~5000)",        // 左括号后有空格
            "价格:(1000~5000 )",        // 右括号前有空格
            "价格:(1000 ~ 5000)",       // 波浪号前后有空格
            "商品类别:[电子产品,家具]",  // 标准格式
            "商品类别 :[电子产品,家具]", // 冒号前有空格
            "商品类别: [电子产品,家具]", // 冒号和方括号之间有空格
            "商品类别:[ 电子产品,家具]", // 左方括号后有空格
            "商品类别:[电子产品,家具 ]", // 右方括号前有空格
            "商品类别:[电子产品 , 家具]" // 逗号前后有空格
        };
        
        for (String expression : expressions) {
            LogicalExpression tree = handler.convertToBinaryTree(expression);
            System.out.println("表达式: " + expression);
            System.out.println("树形结构:\n" + tree.prettyPrint());
            System.out.println();
        }
    }

    /**
     * 测试操作符周围有空格的情况
     */
    private static void testOperatorsWithSpaces() {
        System.out.println("====== 测试操作符周围有空格的情况 ======");
        LogicalExpressionHandler<LogicalExpression> handler = new DefaultLogicalExpressionHandler();
        
        String[] expressions = {
            "价格>1000",               // 无空格
            "价格 > 1000",             // 标准格式
            "价格>= 1000",             // 操作符后有空格
            "价格 >=1000",             // 操作符前有空格
            "价格 < = 1000",           // 操作符内部有空格
            "价格 ! = 1000",           // 不等于操作符内部有空格
            "价格 < > 1000"            // 不等于操作符内部有空格
        };
        
        for (String expression : expressions) {
            LogicalExpression tree = handler.convertToBinaryTree(expression);
            System.out.println("表达式: " + expression);
            System.out.println("树形结构:\n" + tree.prettyPrint());
            System.out.println();
        }
    }

    /**
     * 测试复杂表达式中的空格处理
     */
    private static void testComplexSpaces() {
        System.out.println("====== 测试复杂表达式中的空格处理 ======");
        LogicalExpressionHandler<LogicalExpression> handler = new DefaultLogicalExpressionHandler();
        
        String[] expressions = {
            "(商品名称 : 手机 OR 商品名称 : 平板) AND (价格 >= 1000 AND 价格 <= 5000)",
            "商品名称 LIKE % 手机 %",
            "商品类别 : [ 电子产品 , 家具 , 厨具 ]",
            "创建日期 BETWEEN '2024-04-01' ~ '2024-05-01'",
            "价格 : ( 1000 ~ 5000 )",
            "~商品类别 : 食品"
        };
        
        for (String expression : expressions) {
            LogicalExpression tree = handler.convertToBinaryTree(expression);
            System.out.println("表达式: " + expression);
            System.out.println("树形结构:\n" + tree.prettyPrint());
            System.out.println();
        }
    }
} 