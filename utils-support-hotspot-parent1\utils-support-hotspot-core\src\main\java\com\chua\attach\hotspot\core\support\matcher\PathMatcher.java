package com.chua.attach.hotspot.core.support.matcher;

/**
 * 路径匹配
 *
 * <AUTHOR>
 * @since 1.0
 */
public interface PathMatcher {
    static PathMatcher INSTANCE = new LinkPathMatcher();
    /**
     * 是否满足匹配标准
     *
     * @param path 路径
     * @return boolean
     */
    boolean isPattern(String path);

    /**
     * 匹配
     *
     * @param pattern 正则
     * @param path    路径
     * @return boolean
     */
    boolean match(String pattern, String path);

    /**
     * 匹配
     *
     * @param pattern 正则
     * @param path    路径
     * @return boolean
     */
    boolean matchStart(String pattern, String path);
}
