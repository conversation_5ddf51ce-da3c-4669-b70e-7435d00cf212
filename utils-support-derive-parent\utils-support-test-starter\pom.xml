<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.chua</groupId>
    <version>********</version>

    <!-- 模块基本信息 -->
    <artifactId>utils-support-test-starter</artifactId>
    <name>Utils Support Test Starter</name>
    <description>
        测试工具集成模块，提供全面的测试框架和工具支持。

        主要功能：
        - 🧪 单元测试：JUnit、TestNG 等测试框架集成
        - 🎯 Mock 测试：Mockito、PowerMock 等 Mock 框架
        - 📊 性能测试：JMH、压力测试工具集成
        - 🔍 代码覆盖率：JaCoCo 代码覆盖率统计
        - 📝 测试报告：详细的测试报告生成和分析
        - 🔧 测试工具：断言库、测试数据生成工具
        - 🌐 集成测试：Spring Test、数据库测试支持
        - 🚀 自动化测试：CI/CD 集成和自动化测试

        适用场景：
        - 单元测试开发
        - 集成测试验证
        - 性能基准测试
        - 测试驱动开发
        - 持续集成测试
        - 质量保证体系
    </description>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-exec</artifactId>
            <version>1.5.0</version>
        </dependency>

        <dependency>
            <groupId>commons-cli</groupId>
            <artifactId>commons-cli</artifactId>
            <version>1.9.0</version>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.19.0</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>3.6.0</version>
                <configuration>
                    <archive>
                        <manifest>
                            <mainClass>com.chua.test.NodeMain</mainClass> <!-- 替换为你的主类 -->
                        </manifest>
                    </archive>
                    <descriptorRefs>
                        <descriptorRef>jar-with-dependencies</descriptorRef>
                    </descriptorRefs>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>