#!/bin/bash
export LANG=en_US.UTF-8
export LC_CTYPE=en_US.UTF-8
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.382.b05-1.el7_9.x86_64
export PATH=$JAVA_HOME/bin:$PATH
export LD_LIBRARY_PATH=$JAVA_HOME/lib/server:$LD_LIBRARY_PATH

# 日志目录
LOG_DIR="/zxb2/batch/logs"
echo "日志目录: $LOG_DIR"
# 脚本目录列表
SCRIPT_DIRS=(
  "/zxb2/book"
)

# 创建日志目录（如果不存在）
mkdir -p "$LOG_DIR"

# 获取当前时间作为日志文件名的一部分
TIMESTAMP=$(date +"%Y%m%d")

# 终止包含SCRIPT_DIRS路径的进程
for dir in "${SCRIPT_DIRS[@]}"; do
  echo "查找并终止包含路径 $dir 的进程..."
  
  # 查找包含指定路径的进程
  PIDS=$(ps -ef | grep "$dir" | grep -v grep | awk '{print $2}')
  
  if [ -n "$PIDS" ]; then
    echo "发现以下进程包含路径 $dir: $PIDS"
    
    for pid in $PIDS; do
      echo "正在终止进程 PID: \e[1;31m$pid\e[0m"
      kill -15 $pid
    done
    
    # 等待进程终止
    sleep 2
    
    # 检查进程是否仍在运行，如果是则强制终止
    for pid in $PIDS; do
      if ps -p $pid > /dev/null 2>&1; then
        echo "进程 $pid 未响应，强制终止..."
        kill -9 $pid
      fi
    done
  else
    echo "未发现包含路径 $dir 的进程"
  fi
done

# 定义要启动的脚本列表，格式为 "目录索引:脚本名称"
# 目录索引对应SCRIPT_DIRS数组的索引，从0开始
SCRIPTS=(
  "0:start.sh"
)

# 启动每个脚本
# 遍历每个脚本目录
for dir_index in "${!SCRIPT_DIRS[@]}"; do
  SCRIPT_DIR="${SCRIPT_DIRS[$dir_index]}"
  FOLDER_NAME=$(basename "$SCRIPT_DIR")
  
  echo "处理目录: $SCRIPT_DIR"
  
  # 检查目录是否存在
  if [ ! -d "$SCRIPT_DIR" ]; then
    echo "错误: 目录 $SCRIPT_DIR 不存在"
    continue
  fi
  
  # 定义要启动的脚本列表
  SCRIPTS=("start.sh")
  
  # 启动该目录下的每个脚本
  for script in "${SCRIPTS[@]}"; do
    # 检查脚本是否存在
    if [ -f "$SCRIPT_DIR/$script" ]; then
      echo "正在启动: $SCRIPT_DIR/$script"
      
      # 确保脚本有执行权限
      chmod +x "$SCRIPT_DIR/$script"
      
      # 使用nohup在后台运行脚本，并将输出重定向到日志文件
      # 使用文件夹名称作为日志文件名的一部分
      nohup "$SCRIPT_DIR/$script" > "$LOG_DIR/${FOLDER_NAME}_${script%.*}_$TIMESTAMP.log" 2>&1 &
      
      # 获取进程ID并保存
      PID=$!
      echo "$FOLDER_NAME/$script 已启动，PID: $PID"
      echo "$PID" > "$LOG_DIR/${FOLDER_NAME}_${script%.*}.pid"
      
      # 等待一秒，避免同时启动过多进程
      sleep 1
    else
      echo "错误: 脚本 $SCRIPT_DIR/$script 不存在"
    fi
  done
done

echo "所有脚本已启动完成"
echo "日志文件保存在: $LOG_DIR"