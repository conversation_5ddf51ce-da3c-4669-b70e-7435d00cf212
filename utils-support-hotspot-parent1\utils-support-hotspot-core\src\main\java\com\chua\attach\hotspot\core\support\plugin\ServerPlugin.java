package com.chua.attach.hotspot.core.support.plugin;

import com.chua.attach.hotspot.core.support.endpoint.EndpointFactory;
import com.chua.attach.hotspot.core.support.enums.ModuleType;
import com.chua.attach.hotspot.core.support.server.ServerFactory;
import com.chua.attach.hotspot.core.support.server.ServiceInstance;
import com.chua.attach.hotspot.core.support.span.NewTrackManager;
import com.chua.attach.hotspot.core.support.span.Span;
import com.chua.attach.hotspot.core.support.utils.NetAddress;
import net.bytebuddy.description.type.TypeDescription;
import net.bytebuddy.dynamic.DynamicType;
import net.bytebuddy.implementation.MethodDelegation;
import net.bytebuddy.implementation.bind.annotation.*;
import net.bytebuddy.matcher.ElementMatcher;
import net.bytebuddy.matcher.ElementMatchers;

import java.lang.reflect.Method;
import java.sql.Driver;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
public class ServerPlugin implements Plugin {

    public static final Map<String, AtomicInteger> IN_MEM = new LinkedHashMap<>();
    public static final List<ServiceInstance> SERVER_LIST = new LinkedList<>();
    @Override
    public String name() {
        return "Connection";
    }

    @Override
    public void init() {
    }

    @Override
    public boolean print() {
        return false;
    }

    @Override
    public DynamicType.Builder.MethodDefinition.ReceiverTypeDefinition<?> transform(DynamicType.Builder<?> builder) {
        return builder.method(ElementMatchers.named("connect")).intercept(MethodDelegation.to(ServerPlugin.class));
    }


    @Override
    public ElementMatcher<? super TypeDescription> type() {
        return ElementMatchers.hasSuperType(ElementMatchers.named(Driver.class.getName()));
    }

    @RuntimeType
    public static Object intercept(
            // 被拦截的目标对象 （动态生成的目标对象）
            @This Object target,
            // 正在执行的方法Method 对象（目标对象父类的Method）
            @Origin Method method,
            // 正在执行的方法的全部参数
            @AllArguments Object[] objects,
            // 目标对象的一个代理
            @Super Object delegate,
            // 方法的调用者对象 对原始方法的调用依靠它
            @SuperCall Callable<?> callable) throws Exception {
        try {
            String string = objects[0].toString().toLowerCase();
            NetAddress netAddress = NetAddress.of(string);
            ServiceInstance ss = new ServiceInstance();
            ss.setName(getName(string).toUpperCase());
            ss.setSourceName("HOST");
            ss.setSourceHost(EndpointFactory.getInstance().appHost);
            ss.setSourcePort(Integer.parseInt(EndpointFactory.getInstance().applicationPort));
            ss.setTargetHost(netAddress.getHost());
            ss.setTargetPort(netAddress.getPort());
            send(ss);
        } catch (Exception ignored) {
        }
        return callable.call();
    }

    private static String getName(String string) {
        if(string.contains("mysql")) {
            return "mysql";
        }
        if(string.contains("oracle")) {
            return "oracle";
        }
        return "jdbc";
    }

    public static String getKey(ServiceInstance ss) {
        return ss.getSourceHost() + ss.getSourcePort() + ss.getTargetHost() + ss.getTargetPort() + ss.getName();
    }

    public static void send(ServiceInstance ss) {
        String key = getKey(ss);
        Span currentSpan = NewTrackManager.getCurrentSpan();
        if(null != currentSpan) {
            currentSpan.addServiceInstance(ss);
        }
        if(!IN_MEM.containsKey(key)) {
            SERVER_LIST.add(ss);
            IN_MEM.put(key, new AtomicInteger(1));
            EndpointFactory.getInstance().publish(ModuleType.SERVER, ss);
            ServerFactory.getInstance().publish(ModuleType.SERVER, "AGENT_SERVER", ss);
            return;
        }

        IN_MEM.get(key).incrementAndGet();
    }


    public static List<ServiceInstance> getServerList() {
        for (ServiceInstance serviceInstance : SERVER_LIST) {
            if(null == serviceInstance) {
                continue;
            }
            serviceInstance.setCount(IN_MEM.get(getKey(serviceInstance)).get());
        }

        return SERVER_LIST;
    }


}
