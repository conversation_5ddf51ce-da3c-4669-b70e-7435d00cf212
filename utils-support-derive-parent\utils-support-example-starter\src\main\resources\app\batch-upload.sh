#!/bin/bash

# 批量上传脚本
# 用法: ./batch-upload.sh -f <文件名> -d <远程路径> [-force true/false] [-u true/false]

# 初始化变量
FILE_NAME=""
REMOTE_PATH=""
FORCE_DELETE=true
UNZIP=true
SCRIPT_PATH=""

# 服务器配置列表
declare -A SERVERS
SERVERS=(
  ["server1"]="root:Yaq.K11qiRenWen@*************:22"
)

# 显示使用方法
show_usage() {
  echo "批量上传文件脚本"
  echo "用法: $0 -f <文件名> -d <远程路径> [-force true/false] [-u true/false] [-s <脚本路径>]"
  echo "参数:"
  echo "  -f      要上传的文件名 (必须)"
  echo "  -d      远程服务器上的目标路径 (必须)"
  echo "  -force  是否删除远程目录下所有文件 (可选，默认: true)"
  echo "  -u      是否解压上传的压缩包 (可选，默认: true)"
  echo "  -s      上传成功后执行的脚本路径 (可选)"
  echo "示例:"
  echo "  $0 -f data.zip -d /home/<USER>/uploads -s /path/to/script.sh"
  exit 1
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case "$1" in
    -f)
      FILE_NAME="$2"
      shift 2
      ;;
    -d)
      REMOTE_PATH="$2"
      shift 2
      ;;
    -force)
      FORCE_DELETE="$2"
      shift 2
      ;;
    -u)
      UNZIP="$2"
      shift 2
      ;;
    -s)
      SCRIPT_PATH="$2"
      shift 2
      ;;
    *)
      echo "未知参数: $1"
      show_usage
      ;;
  esac
done

# 检查必要参数
if [ -z "$FILE_NAME" ] || [ -z "$REMOTE_PATH" ]; then
  echo "错误: 必须提供文件名 (-f) 和远程路径 (-d)"
  show_usage
fi

# 检查文件是否存在
if [ ! -f "$FILE_NAME" ] && [ ! -d "$FILE_NAME" ]; then
  echo "错误: 文件或目录 '$FILE_NAME' 不存在"
  exit 1
fi

# 显示上传信息
echo "准备上传文件..."
echo "文件/目录: $FILE_NAME"
echo "目标路径: $REMOTE_PATH"
echo "强制删除远程文件: $FORCE_DELETE"
echo "解压文件: $UNZIP"
echo "服务器数量: ${#SERVERS[@]}"

# 确认上传
read -p "是否继续上传? (y/n): " confirm
if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
  echo "上传已取消"
  exit 0
fi

# 创建临时脚本文件
TEMP_SCRIPT=$(mktemp)
chmod +x $TEMP_SCRIPT

# 遍历所有服务器进行上传
for server_name in "${!SERVERS[@]}"; do
  server_info="${SERVERS[$server_name]}"
  
  # 解析服务器信息
  username=$(echo $server_info | cut -d':' -f1)
  password=$(echo $server_info | cut -d':' -f2 | cut -d'@' -f1)
  host=$(echo $server_info | cut -d'@' -f2 | cut -d':' -f1)
  port=$(echo $server_info | cut -d':' -f3)
  
  echo "正在处理服务器: $server_name ($host)"
  
  # 创建远程命令脚本
  cat > $TEMP_SCRIPT << EOF
#!/bin/bash
# 创建远程目录
mkdir -p "$REMOTE_PATH"

# 如果需要删除远程目录下的所有文件
if [ "$FORCE_DELETE" = "true" ]; then
  echo "删除远程目录下的所有文件..."
  rm -rf "$REMOTE_PATH"/*
fi
EOF

  # 使用sshpass执行远程命令
  echo "创建远程目录并准备环境..."
  sshpass -p "$password" ssh -o StrictHostKeyChecking=no -p $port $username@$host "bash -s" < $TEMP_SCRIPT
  
  if [ $? -ne 0 ]; then
    echo "错误: 无法连接到服务器 $server_name ($host)"
    continue
  fi
  
  # 上传文件
  echo "开始上传文件到 $server_name ($host)..."
  if [ -d "$FILE_NAME" ]; then
    # 上传目录
    sshpass -p "$password" scp -o StrictHostKeyChecking=no -P $port -r "$FILE_NAME" $username@$host:"$REMOTE_PATH"
  else
    # 上传文件
    sshpass -p "$password" scp -o StrictHostKeyChecking=no -P $port "$FILE_NAME" $username@$host:"$REMOTE_PATH"
  fi
  
  # 检查上传结果
  if [ $? -eq 0 ]; then
    echo "上传成功: $FILE_NAME 已上传到 $server_name ($host):$REMOTE_PATH"
    
    # 如果需要解压文件
    if [ "$UNZIP" = "true" ]; then
      echo "正在解压文件..."
      filename=$(basename "$FILE_NAME")
      
      # 创建解压命令脚本
      cat > $TEMP_SCRIPT << EOF
#!/bin/bash
cd "$REMOTE_PATH"
if [[ "$filename" == *.zip ]]; then
  unzip -o "$filename"
  echo "ZIP文件解压完成"
elif [[ "$filename" == *.tar.gz ]] || [[ "$filename" == *.tgz ]]; then
  tar -xzf "$filename"
  echo "TAR.GZ文件解压完成"
elif [[ "$filename" == *.tar ]]; then
  tar -xf "$filename"
  echo "TAR文件解压完成"
else
  echo "不支持的压缩格式，无法解压"
  exit 1
fi
EOF
      
      # 执行解压命令
      sshpass -p "$password" ssh -o StrictHostKeyChecking=no -p $port $username@$host "bash -s" < $TEMP_SCRIPT
      
      if [ $? -eq 0 ]; then
        echo "文件解压成功"
      else
        echo "文件解压失败"
      fi

      # 如果指定了脚本，则执行
      if [ ! -z "$SCRIPT_PATH" ]; then
        echo "执行后续脚本: $SCRIPT_PATH"
        # 上传脚本
        sshpass -p "$password" scp -o StrictHostKeyChecking=no -P $port "$SCRIPT_PATH" $username@$host:"$REMOTE_PATH/post_script.sh"
        
        # 执行脚本
        cat > $TEMP_SCRIPT << EOF
#!/bin/bash
cd "$REMOTE_PATH"
chmod +x post_script.sh
./post_script.sh
rm -f post_script.sh
EOF

     # 执行脚本   
        sshpass -p "$password" ssh -o StrictHostKeyChecking=no -p $port $username@$host "bash -s" < $TEMP_SCRIPT
        
        if [ $? -eq 0 ]; then
          echo "后续脚本执行成功"
        else
          echo "后续脚本执行失败"
        fi
      fi
        
    fi
  else
    echo "上传失败: 无法上传 $FILE_NAME 到 $server_name ($host)"
  fi
  
  echo "----------------------------------------"
done

# 删除临时脚本
rm -f $TEMP_SCRIPT

# 记录上传日志
LOG_FILE="upload_log.txt"
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 批量上传: $FILE_NAME -> $REMOTE_PATH (Force: $FORCE_DELETE, Unzip: $UNZIP)" >> $LOG_FILE
echo "上传记录已保存到 $LOG_FILE"

echo "批量上传完成"
exit 0