package com.chua.common.support.protocol.channel;

/**
 * Channel接口定义了通道的行为，通道可以设置监听器以接收特定事件。
 *
 * 通道是通信或数据传输的抽象概念，通过通道，可以实现不同组件或系统之间的数据交互和事件通知。
 * 该接口的实现类可以根据具体需求，定义并实现数据的传输方式和事件通知机制。
 *
 * <AUTHOR>
 * @since 2024/6/28
 */
public interface Channel extends AutoCloseable{

    /**
     * 设置通道监听器。
     *
     * 通过该方法，可以为通道设置一个监听器，监听器用于接收通道发生的特定事件。
     * 实现类可以根据需求，定义具体的事件类型和触发事件的条件。
     *
     * @param listener 通道监听器，用于接收和处理通道事件。
     */
    void setListener(ChannelListener listener);


    /**
     * 执行命令并返回执行结果。
     *
     * 该方法用于执行指定的命令，并返回执行结果。
     * 实现类可以根据需求，定义具体的命令执行方式，并将执行结果返回给调用方。
     *
     * @param command 要执行的命令。
     * @param timeoutMill 命令执行的超时时间（毫秒）。
     * @return 执行命令后的结果。
     */
    String execute(String command, int timeoutMill);

    /**
     * 执行命令并返回执行结果。
     * <p>
     * 该方法用于执行指定的命令，并返回执行结果。
     * 实现类可以根据需求，定义具体的命令执行方式，并将执行结果返回给调用方。
     *
     * @param command 要执行的命令。
     * @return 执行命令后的结果。
     */
    default String execute(String command) {
        return execute(command, 300_000);
    }
    /**
     * 是否关闭
     * @return 是否关闭
     */
    boolean isClose();
}
