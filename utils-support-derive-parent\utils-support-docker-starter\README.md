# Utils Support Docker Starter

基于 Docker Java API 的 Docker 容器化支持模块，提供完整的 Docker 客户端功能。

## 功能特性

- 🐳 **容器管理**: 创建、启动、停止、删除、重启容器
- 📦 **镜像管理**: 拉取、构建、推送、删除、标记镜像
- 🔍 **容器监控**: 获取容器状态、日志、统计信息
- 📊 **系统信息**: 获取 Docker 系统信息和版本
- 🌐 **网络管理**: 创建、删除、管理 Docker 网络
- 💾 **卷管理**: 创建、删除、管理 Docker 卷
- 🔧 **命令执行**: 在容器中执行命令
- 📋 **事件监听**: 监听 Docker 事件
- ⚙️ **高级配置**: 支持端口映射、环境变量、卷挂载等

## 依赖说明

本模块依赖于：
- Docker Java API (3.4.0)
- utils-support-common-starter
- Apache HttpClient 5

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>com.chua</groupId>
    <artifactId>utils-support-docker-starter</artifactId>
    <version>********</version>
</dependency>
```

### 2. 基本使用

```java
// 创建客户端设置
ClientSetting clientSetting = ClientSetting.builder()
    .url("tcp://localhost:2376")  // Docker 守护进程地址
    .build();

// 创建 Docker 客户端
try (DockerClient dockerClient = new DockerClient(clientSetting)) {
    // 测试连接
    boolean connected = dockerClient.testConnection();
    
    // 获取 Docker 信息
    Info dockerInfo = dockerClient.getDockerInfo();
    System.out.println("Docker 版本: " + dockerInfo.getServerVersion());
}
```

### 3. 容器管理

```java
try (DockerClient dockerClient = new DockerClient(clientSetting)) {
    // 拉取镜像
    dockerClient.pullImage("nginx", "latest");
    
    // 创建容器
    CreateContainerResponse container = dockerClient.createContainer(
        "nginx:latest", "my-nginx");
    String containerId = container.getId();
    
    // 启动容器
    dockerClient.startContainer(containerId);
    
    // 获取容器信息
    InspectContainerResponse info = dockerClient.inspectContainer(containerId);
    
    // 停止并删除容器
    dockerClient.stopContainer(containerId, 10);
    dockerClient.removeContainer(containerId, true, true);
}
```

### 4. 高级容器配置

```java
// 端口映射
Map<String, String> portBindings = new HashMap<>();
portBindings.put("80", "8080");  // 容器80端口映射到主机8080端口

// 环境变量
List<String> env = Arrays.asList("ENV_VAR=value");

// 卷挂载
Map<String, String> volumes = new HashMap<>();
volumes.put("/container/path", "/host/path");

// 创建容器
CreateContainerResponse container = dockerClient.createContainer(
    "nginx:latest", "my-nginx", portBindings, env, volumes);
```

### 5. 容器交互

```java
// 创建会话
DockerChannelSession session = dockerClient.createSession();

// 在容器中执行命令
String result = session.executeCommand(containerId, 
    new String[]{"ls", "-la"}, 10);

// 获取容器日志
String logs = session.getContainerLogs(containerId, 50, false);

// 获取容器统计信息
Statistics stats = session.getContainerStats(containerId);
```

### 6. 镜像管理

```java
try (DockerClient dockerClient = new DockerClient(clientSetting)) {
    // 列出镜像
    List<Image> images = dockerClient.listImages();
    
    // 拉取镜像
    dockerClient.pullImage("alpine", "latest");
    
    // 获取镜像信息
    InspectImageResponse imageInfo = dockerClient.inspectImage("alpine:latest");
    
    // 标记镜像
    dockerClient.tagImage("alpine:latest", "my-alpine", "v1.0");
    
    // 删除镜像
    dockerClient.removeImage("my-alpine:v1.0", false);
}
```

### 7. 网络管理

```java
try (DockerClient dockerClient = new DockerClient(clientSetting)) {
    // 创建网络
    CreateNetworkResponse network = dockerClient.createNetwork("my-network", "bridge");
    
    // 列出网络
    List<Network> networks = dockerClient.listNetworks();
    
    // 删除网络
    dockerClient.removeNetwork(network.getId());
}
```

### 8. 卷管理

```java
try (DockerClient dockerClient = new DockerClient(clientSetting)) {
    // 创建卷
    CreateVolumeResponse volume = dockerClient.createVolume("my-volume", "local");

    // 列出卷
    ListVolumesResponse volumes = dockerClient.listVolumes();

    // 删除卷
    dockerClient.removeVolume("my-volume", false);
}
```

### 9. 镜像搜索和版本管理

```java
try (DockerClient dockerClient = new DockerClient(clientSetting)) {
    // 模糊搜索镜像
    List<Image> searchResults = dockerClient.searchImagesByName("nginx");
    searchResults.forEach(image -> {
        if (image.getRepoTags() != null) {
            Arrays.stream(image.getRepoTags()).forEach(tag -> {
                System.out.println("找到镜像: " + tag);
            });
        }
    });

    // 获取指定镜像的所有版本
    List<String> versions = dockerClient.getImageVersions("nginx");
    versions.forEach(version -> System.out.println("版本: " + version));

    // 获取镜像版本详细信息
    List<DockerClient.ImageVersionInfo> versionDetails =
            dockerClient.getImageVersionDetails("nginx");
    versionDetails.forEach(info -> {
        System.out.println("镜像: " + info.toString());
        System.out.println("大小: " + info.getSizeFormatted());
        System.out.println("创建时间: " + new Date(info.getCreated() * 1000));
    });
}
```

## 配置选项

### ClientSetting 配置

```java
ClientSetting clientSetting = ClientSetting.builder()
    .url("tcp://localhost:2376")           // Docker 守护进程地址
    .host("localhost")                     // Docker 主机
    .port(2376)                           // Docker 端口
    .ssl(true)                            // 是否使用 TLS
    .certPath("/path/to/certs")           // 证书路径
    .version("1.41")                      // API 版本
    .username("dockeruser")               // 注册表用户名
    .password("dockerpass")               // 注册表密码
    .email("<EMAIL>")            // 注册表邮箱
    .connectTimeoutMillis(30000)          // 连接超时
    .build();
```

### Docker 主机配置

支持多种 Docker 主机连接方式：

```java
// TCP 连接
.url("tcp://localhost:2376")

// Unix Socket 连接（Linux/Mac）
.url("unix:///var/run/docker.sock")

// Windows Named Pipe
.url("npipe:////./pipe/docker_engine")

// TLS 加密连接
.url("tcp://localhost:2376")
.ssl(true)
.certPath("/path/to/docker/certs")
```

## 注意事项

1. **Docker 守护进程**: 确保 Docker 守护进程正在运行
2. **权限配置**: 确保有足够的权限访问 Docker API
3. **网络连接**: 检查网络连接和防火墙设置
4. **资源管理**: 及时关闭客户端连接以释放资源
5. **异常处理**: 妥善处理 Docker 操作可能抛出的异常

## 完整示例

参考 `DockerExample` 类获取完整的使用示例，包括：
- 基本 Docker 操作
- 容器生命周期管理
- 镜像管理操作
- 网络和卷管理

## 故障排除

### 常见问题

1. **连接失败**: 检查 Docker 守护进程是否运行，端口是否正确
2. **权限错误**: 确保用户有 Docker 操作权限
3. **镜像拉取失败**: 检查网络连接和镜像仓库访问权限
4. **容器启动失败**: 检查镜像是否存在，端口是否被占用

### 日志配置

```xml
<logger name="com.chua.docker" level="DEBUG"/>
<logger name="com.github.dockerjava" level="INFO"/>
```
