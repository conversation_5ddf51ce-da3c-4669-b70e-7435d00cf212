package com.chua.attach.hotspot.core.support.link;

import com.chua.attach.hotspot.core.support.span.Span;
import com.chua.attach.hotspot.core.support.utils.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface LinkResolver {
    /**
     * 获取链接id
     *
     * @param args args
     * @return {@link String}
     */
    String getLinkId(Object[] args);


    /**
     * 具有链接id
     *
     * @param args args
     * @return boolean
     */
    default boolean hasLinkId(Object[] args) {
        return !StringUtils.isBlank(getLinkId(args));
    }
    /**
     * 具有链接父id
     *
     * @param args args
     * @return {@link String}
     */
    String getLinkParentId(Object[] args);

    /**
     * 具有链接父id
     *
     * @param args args
     * @return boolean
     */
    default boolean hasLinkParentId(Object[] args) {
        return !StringUtils.isBlank(getLinkParentId(args));

    }

    void sendResponse(List<Span> spans, Object response);
}
